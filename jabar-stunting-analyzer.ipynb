{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["# **🎯 JABAR BALITA STUNTING ANALYZER**\n", "## **Sistem Analisis <PERSON> Stunting Jawa Barat**\n", "\n", "### **🤔 APA ITU JABAR BALITA STUNTING ANALYZER?**\n", "Sistem analisis yang membantu mengidentifikasi dan membandingkan tingkat stunting balita di 45 kabupaten/kota Jawa Barat untuk menentukan daerah mana yang memiliki stunting lebih banyak atau lebih sedikit.\n", "\n", "### **👥 UNTUK SIAPA SISTEM INI?**\n", "- 🏛️ **Pemerintah Provinsi & Kabupaten/Kota** - Untuk kebijakan dan alokasi anggaran\n", "- 🏥 **<PERSON><PERSON>** - Untuk program intervensi dan monitoring\n", "- 👨‍⚕️ **<PERSON><PERSON>** - Untuk identifikasi daerah prioritas\n", "- 📊 **<PERSON> & <PERSON>** - Untuk analisis dan riset lanjutan\n", "\n", "### **⚡ MANFAAT UTAMA (DALAM 5 MENIT!):**\n", "- ✅ **Ranking Lengkap** da<PERSON>h dari stunting tertinggi ke terendah\n", "- ✅ **<PERSON><PERSON><PERSON><PERSON>** jumlah stunting vs non-stunting per daerah\n", "- ✅ **<PERSON><PERSON>** untuk visualisasi geografis yang mudah dipahami\n", "- ✅ **<PERSON><PERSON>an Excel** dengan tabel perbandingan komprehensif\n", "- ✅ **Identifikasi Cepat** daerah yang perlu perhatian khusus\n", "\n", "---\n", "\n", "## **📋 Informasi Project**\n", "\n", "**🎯 <PERSON><PERSON><PERSON>:** Menganalisis dan membandingkan tingkat stunting balita di 45 kabupaten/kota Jawa Barat untuk mengidentifikasi daerah mana yang memiliki jumlah stunting lebih banyak dan lebih sedikit, serta memberikan ranking daerah berdasarkan tingkat stunting.\n", "\n", "**📊 Dataset:** `<PERSON><PERSON>_Stunting_<PERSON><PERSON><PERSON>_Jawa_Barat.csv`\n", "\n", "**🤖 Model ML:** Decision Tree Classifier, K-Nearest Neighbors (KNN), Gaussian Naive <PERSON>\n", "\n", "**📈 Output:** Ranking <PERSON><PERSON>h Stunting <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> Stunting vs Non-Stunting per <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> Excel Ko<PERSON>hensif\n", "\n", "---\n", "\n", "## **👥 <PERSON> - <PERSON> 9**\n", "- **<PERSON><PERSON>**\n", "- **<PERSON>**\n", "- **<PERSON><PERSON>**\n", "\n", "---\n", "\n", "## **🛠️ Pembuatan Sistem - Artificial intelligence**\n", "- Menggunakan model AI dari Google yaitu Google Gemini\n", "- Menggunakan Tools AI dari Github yaitu Github Copilot yang di dalamnya kita menggunakan model AI dari Anthropic yaitu Claude Sonnet 4\n", "\n", "---\n", "\n", "## **📜 Riwayat Prompt - Artificial intelligence**\n", "- Pembuatan Dataset = https://g.co/gemini/share/756d9990197a\n", "- Pembuatan Sistem via Google Gemini = https://g.co/gemini/share/071dd02a4ff1\n", "- Pembuatan Sistem via Github Copilot = Tidak tersedia dikarenakan Github Copilot tidak bisa untuk mengshare riwayat promptnya\n", "\n", "\n", "---\n", "\n", "## **📝 <PERSON> (12 <PERSON><PERSON><PERSON>)**\n", "1. **🔧 Setup** - Persiapan sistem dan library\n", "2. **📊 Load Data** - Memuat data stunting Jawa Barat\n", "3. **🧹 Bersihkan Data** - Mengatasi data yang hilang/tidak valid\n", "4. **🔍 Eksplorasi** - Melihat pola dan distribusi data\n", "5. **⚙️ Persiapan AI** - Menyiapkan data untuk kecerdasan buatan\n", "6. **🤖 Training AI** - Melatih 3 algoritma pintar\n", "7. **🏆 <PERSON><PERSON><PERSON>** - <PERSON><PERSON><PERSON><PERSON> algoritma dengan a<PERSON>si tertinggi\n", "8. **📈 <PERSON><PERSON>asi** - <PERSON><PERSON><PERSON><PERSON> <PERSON>a prediksi\n", "9. **🗺️ <PERSON><PERSON><PERSON>** - <PERSON><PERSON><PERSON><PERSON> setiap kabupaten/kota\n", "10. **📋 <PERSON><PERSON>t <PERSON>** - Membuat laporan leng<PERSON> + Export Excel\n", "11. **🎨 Visualisasi** - <PERSON><PERSON> interaktif dan grafik mudah dipahami\n", "12. **🎯 <PERSON><PERSON><PERSON><PERSON>n** - <PERSON><PERSON><PERSON> hasil dan insights utama"], "metadata": {"id": "header_project"}}, {"cell_type": "markdown", "source": ["## **📚 Langkah 1: Persiapan Environment dan Library**\n", "\n", "**Tujuan:** Mempersiapkan semua library yang diperlukan untuk analisis data stunting dan implementasi machine learning\n", "\n", "**Library yang digunakan:**\n", "- **pandas**: <PERSON><PERSON><PERSON><PERSON> dan analisis data\n", "- **numpy**: Operasi numerik dan array\n", "- **mat<PERSON><PERSON><PERSON><PERSON> & seaborn**: Visualisasi data dan grafik\n", "- **scikit-learn**: Algoritma machine learning dan evaluasi model\n", "- **tabulate**: Format tabel yang profesional dan mudah dibaca\n", "- **folium**: <PERSON><PERSON><PERSON>\n", "- **warnings**: Mengkontrol peringatan pada sistem\n", "- **time**: <PERSON><PERSON><PERSON><PERSON> waktu pada sistem  "], "metadata": {"id": "step1_header"}}, {"cell_type": "code", "source": ["# Install library tambahan yang diperlukan untuk analisis\n", "!pip install tabulate seaborn folium plotly openpyxl -q\n", "\n", "# Import library untuk manipulasi dan analisis data\n", "import pandas as pd\n", "import numpy as np\n", "\n", "# Import library untuk visualisasi data\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "\n", "# Import library untuk visualisasi peta interaktif\n", "import folium\n", "from folium import plugins\n", "\n", "# Import library untuk machine learning\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import LabelEncoder, StandardScaler\n", "from sklearn.tree import DecisionTreeClassifier\n", "from sklearn.neighbors import KNeighborsClassifier\n", "from sklearn.naive_bayes import GaussianNB\n", "from sklearn.metrics import (\n", "    accuracy_score, precision_score, recall_score, f1_score,\n", "    classification_report, confusion_matrix\n", ")\n", "\n", "# Import library untuk format tabel profesional\n", "from tabulate import tabulate\n", "\n", "# Import library untuk pengukuran waktu eksekusi\n", "import time\n", "import warnings\n", "warnings.filterwarnings('ignore')  # Menyembunyikan warning yang tidak perlu\n", "\n", "# Konfigurasi tampilan untuk visualisasi yang optimal\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "\n", "print(\"✅ Semua library berhasil diimport dan dikonfigurasi!\")\n", "print(\"🚀 Environment siap untuk analisis stunting Jawa Barat\")\n", "print(\"📊 Sistem JABAR STUNTING ANALYZER siap dijalankan\")\n", "print(\"🗺️ Library peta interaktif (folium) dan scatter plot (plotly) siap!\")"], "metadata": {"id": "import_libraries"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## **📊 Langkah 2: <PERSON><PERSON>ua<PERSON> dan Eksplorasi Dataset**\n", "\n", "**Tujuan:** Memuat dataset stunting Jawa Barat dan melakukan eksplorasi awal untuk memahami struktur dan karakteristik data\n", "\n", "**Dataset:** `<PERSON><PERSON>_Stunting_<PERSON><PERSON><PERSON>_Jawa_Barat.csv`\n", "\n", "**Kolom Dataset:**\n", "- `nama_kabupaten_kota`: <PERSON>a kabupaten/kota di Jawa Barat\n", "- `tahun`: <PERSON><PERSON> pengambilan data\n", "- `tinggi_badan_cm`: Tinggi badan balita dalam cm\n", "- `berat_badan_kg`: Berat badan balita dalam kg\n", "- `jeni<PERSON>_kelamin`: <PERSON><PERSON>ela<PERSON> (L/P)\n", "- `usia_balita_bulan`: Usia balita dalam bulan\n", "- `status`: Status stunting (Stunting/Non-Stunting)"], "metadata": {"id": "step2_header"}}, {"cell_type": "code", "source": ["# Memuat dataset stunting b<PERSON><PERSON>\n", "print(\"📂 Memuat dataset stunting balita Jawa Barat...\")\n", "df = pd.read_csv('<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_J<PERSON>_Barat.csv')\n", "\n", "print(\"✅ Dataset berhasil dimuat!\")\n", "print(f\"📊 Ukuran dataset: {df.shape[0]} baris, {df.shape[1]} kolom\")\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"📋 INFORMASI DASAR DATASET\")\n", "print(\"=\"*60)\n", "\n", "# Tampilkan 5 baris pertama dataset\n", "print(\"\\n📝 5 Baris Pertama Dataset:\")\n", "print(df.head())\n", "\n", "# Informasi detail tentang dataset\n", "print(\"\\n📊 Informasi Kolom:\")\n", "print(df.info())\n", "\n", "# Statistik deskriptif untuk kolom numerik\n", "print(\"\\n📈 Statistik Deskriptif:\")\n", "print(df.describe())\n", "\n", "# Nilai unik per kolom\n", "print(\"\\n🔍 Nilai Unik per Kolom:\")\n", "for col in df.columns:\n", "    unique_count = df[col].nunique()\n", "    print(f\"  {col}: {unique_count} nilai unik\")\n", "\n", "# Cek missing values\n", "print(\"\\n⚠️ Missing Values:\")\n", "missing_data = df.isnull().sum()\n", "missing_percent = (missing_data / len(df)) * 100\n", "missing_df = pd.DataFrame({\n", "    'Kolom': missing_data.index,\n", "    'Jumlah Missing': missing_data.values,\n", "    'Persentase (%)': missing_percent.values\n", "})\n", "print(missing_df[missing_df['Ju<PERSON>lah Missing'] > 0])\n", "\n", "print(\"\\n✅ Eksplorasi dataset awal selesai!\")"], "metadata": {"id": "load_dataset"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## **🔧 Langkah 3: Preprocessing dan <PERSON><PERSON> Data**\n", "\n", "**Tujuan:** Membersihkan data dan mempersiapkan untuk analisis machine learning yang akurat\n", "\n", "**Proses yang di<PERSON>n:**\n", "- Men<PERSON>ni missing values dengan metode yang tepat\n", "- Standardisasi format jenis kelamin\n", "- <PERSON><PERSON><PERSON><PERSON> fitur ta<PERSON> (BMI, kategori usia)\n", "- Validasi dan pembersihan data outlier"], "metadata": {"id": "step3_header"}}, {"cell_type": "code", "source": ["print(\"🔧 Memulai Data Preprocessing dan <PERSON>...\")\n", "print(\"=\"*50)\n", "\n", "# Buat copy dataset untuk preprocessing\n", "df_clean = df.copy()\n", "\n", "# 1. <PERSON><PERSON> missing values dengan metode yang tepat\n", "print(\"\\n1️⃣ Menangani Missing Values:\")\n", "print(f\"   Missing values sebelum: {df_clean.isnull().sum().sum()}\")\n", "\n", "# Isi missing values dengan median untuk kolom numerik (lebih robust terhadap outlier)\n", "numeric_cols = ['tinggi_badan_cm', 'berat_badan_kg']\n", "for col in numeric_cols:\n", "    if df_clean[col].isnull().sum() > 0:\n", "        median_val = df_clean[col].median()\n", "        df_clean[col].fillna(median_val, inplace=True)\n", "        print(f\"   ✅ {col}: diisi dengan median ({median_val})\")\n", "\n", "print(f\"   Missing values setelah: {df_clean.isnull().sum().sum()}\")\n", "\n", "# 2. <PERSON><PERSON><PERSON> jenis kelamin ke format yang konsisten\n", "print(\"\\n2️⃣ Standardisasi Jen<PERSON>:\")\n", "print(f\"   Format sebelum: {df_clean['jeni<PERSON>_kelamin'].unique()}\")\n", "\n", "# Mapping jenis kelamin ke format standar\n", "gender_mapping = {\n", "    'L': 'Laki-laki',\n", "    'P': '<PERSON><PERSON><PERSON><PERSON>',\n", "    'Laki-laki': 'Laki-laki',\n", "    'Perempuan': 'Perempuan'\n", "}\n", "df_clean['jenis_kelamin'] = df_clean['jenis_kelamin'].map(gender_mapping)\n", "print(f\"   Format setelah: {df_clean['jenis_kelamin'].unique()}\")\n", "\n", "# 3. <PERSON><PERSON><PERSON> fitur tambahan untuk analisis yang lebih mendalam\n", "print(\"\\n3️⃣ Membuat Fitur Tambahan:\")\n", "\n", "# BMI (Body Mass Index) - in<PERSON><PERSON><PERSON> penting untuk status gizi\n", "df_clean['tinggi_m'] = df_clean['tinggi_badan_cm'] / 100\n", "df_clean['bmi'] = df_clean['berat_badan_kg'] / (df_clean['tinggi_m'] ** 2)\n", "print(f\"   ✅ BMI: rata-rata {df_clean['bmi'].mean():.2f}\")\n", "\n", "# Kategori usia untuk analisis yang lebih spesifik\n", "def kategorikan_usia(usia):\n", "    if usia <= 12:\n", "        return '0-12 bulan'\n", "    elif usia <= 24:\n", "        return '13-24 bulan'\n", "    elif usia <= 36:\n", "        return '25-36 bulan'\n", "    elif usia <= 48:\n", "        return '37-48 bulan'\n", "    else:\n", "        return '49-60 bulan'\n", "\n", "df_clean['kategori_usia'] = df_clean['usia_balita_bulan'].apply(kategorikan_usia)\n", "print(f\"   ✅ Kategori usia: {df_clean['kategori_usia'].nunique()} kategori\")\n", "\n", "# Kategori BMI berdasarkan standar WHO untuk balita\n", "def kate<PERSON>ikan_bmi(bmi):\n", "    if bmi < 16:\n", "        return '<PERSON><PERSON>'\n", "    elif bmi < 18.5:\n", "        return '<PERSON><PERSON>'\n", "    elif bmi < 25:\n", "        return 'Normal'\n", "    elif bmi < 30:\n", "        return '<PERSON><PERSON><PERSON>'\n", "    else:\n", "        return '<PERSON>bes<PERSON>'\n", "\n", "df_clean['kategori_bmi'] = df_clean['bmi'].apply(kategorikan_bmi)\n", "print(f\"   ✅ Kategori BMI: {df_clean['kategori_bmi'].nunique()} kategori\")\n", "\n", "print(\"\\n✅ Data preprocessing selesai!\")\n", "print(f\"📊 Dataset final: {df_clean.shape[0]} baris, {df_clean.shape[1]} kolom\")\n", "print(f\"📋 Kolom baru: tinggi_m, bmi, kategori_usia, kategori_bmi\")"], "metadata": {"id": "data_preprocessing"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## **📊 Langkah 4: <PERSON><PERSON>is Eksploratori Data (EDA)**\n", "\n", "**Tujuan:** Menganalisis distribusi data dan pola stunting di Jawa Barat untuk mendapatkan insights awal\n", "\n", "**<PERSON><PERSON><PERSON> yang <PERSON>:**\n", "- Distribusi status stunting secara k<PERSON><PERSON><PERSON>han\n", "- Analisis stunting per kabupaten/kota\n", "- Distribusi berdasarkan karakteristik balita (j<PERSON><PERSON>, usia, BMI)\n", "- Identifikasi pola dan tren data"], "metadata": {"id": "step4_header"}}, {"cell_type": "code", "source": ["print(\"📊 Memulai Exploratory Data Analysis (EDA)...\")\n", "print(\"=\"*60)\n", "\n", "# 1. <PERSON><PERSON><PERSON> distribusi status stunting secara k<PERSON><PERSON><PERSON>han\n", "print(\"\\n1️⃣ DISTRIBUSI STATUS STUNTING JAWA BARAT\")\n", "print(\"-\" * 40)\n", "status_counts = df_clean['status'].value_counts()\n", "status_percent = df_clean['status'].value_counts(normalize=True) * 100\n", "\n", "for status in status_counts.index:\n", "    count = status_counts[status]\n", "    percent = status_percent[status]\n", "    print(f\"   {status}: {count:,} balita ({percent:.2f}%)\")\n", "\n", "# 2. <PERSON><PERSON><PERSON> stunting per kabupaten/kota\n", "print(\"\\n2️⃣ ANALISIS PER KABUPATEN/KOTA\")\n", "print(\"-\" * 40)\n", "print(f\"   Total kabupaten/kota: {df_clean['nama_kabupaten_kota'].nunique()}\")\n", "\n", "# Hitung tingkat stunting per daerah\n", "regional_analysis = df_clean.groupby('nama_kabupaten_kota').agg({\n", "    'status': ['count', lambda x: (x == 'Stunting').sum()]\n", "}).round(2)\n", "\n", "regional_analysis.columns = ['Total_Balita', 'Jumlah_Stunting']\n", "regional_analysis['Persentase_Stunting'] = (regional_analysis['<PERSON><PERSON><PERSON>_Stunting'] / regional_analysis['Total_Balita'] * 100).round(2)\n", "regional_analysis = regional_analysis.sort_values('Persentase_Stunting', ascending=False)\n", "\n", "print(f\"   Rata-rata stunting <PERSON><PERSON>: {regional_analysis['Persentase_Stunting'].mean():.2f}%\")\n", "print(f\"   <PERSON><PERSON><PERSON> dengan stunting tertinggi: {regional_analysis.index[0]} ({regional_analysis['Persentase_Stunting'].iloc[0]:.2f}%)\")\n", "print(f\"   <PERSON><PERSON><PERSON> dengan stunting terendah: {regional_analysis.index[-1]} ({regional_analysis['Persentase_Stunting'].iloc[-1]:.2f}%)\")\n", "\n", "# 3. <PERSON><PERSON><PERSON> ka<PERSON> balita\n", "print(\"\\n3️⃣ ANALISIS BERDASARKAN KARAKTERISTIK BALITA\")\n", "print(\"-\" * 50)\n", "\n", "# <PERSON><PERSON><PERSON><PERSON> jenis k<PERSON>\n", "gender_stunting = df_clean.groupby(['jenis_kelamin', 'status']).size().unstack(fill_value=0)\n", "gender_stunting['Total'] = gender_stunting.sum(axis=1)\n", "gender_stunting['Persentase_Stunting'] = (gender_stunting['Stunting'] / gender_stunting['Total'] * 100).round(2)\n", "\n", "print(\"   👶 Berdasarkan Jen<PERSON>\")\n", "for gender in gender_stunting.index:\n", "    stunting_count = gender_stunting.loc[gender, 'Stunting']\n", "    total_count = gender_stunting.loc[gender, 'Total']\n", "    percentage = gender_stunting.loc[gender, 'Persentase_Stunting']\n", "    print(f\"   {gender}: {stunting_count}/{total_count} balita ({percentage}%)\")\n", "\n", "# <PERSON><PERSON><PERSON><PERSON> kategori usia\n", "age_stunting = df_clean.groupby(['kategori_usia', 'status']).size().unstack(fill_value=0)\n", "age_stunting['Total'] = age_stunting.sum(axis=1)\n", "age_stunting['Persentase_Stunting'] = (age_stunting['Stunting'] / age_stunting['Total'] * 100).round(2)\n", "\n", "print(\"\\n   📅 Berdasarkan Kategori Usia:\")\n", "for age_cat in age_stunting.index:\n", "    stunting_count = age_stunting.loc[age_cat, 'Stunting']\n", "    total_count = age_stunting.loc[age_cat, 'Total']\n", "    percentage = age_stunting.loc[age_cat, 'Persentase_Stunting']\n", "    print(f\"   {age_cat}: {stunting_count}/{total_count} balita ({percentage}%)\")\n", "\n", "# Berda<PERSON><PERSON> kategori BMI\n", "bmi_stunting = df_clean.groupby(['kategori_bmi', 'status']).size().unstack(fill_value=0)\n", "bmi_stunting['Total'] = bmi_stunting.sum(axis=1)\n", "bmi_stunting['Persentase_Stunting'] = (bmi_stunting['Stunting'] / bmi_stunting['Total'] * 100).round(2)\n", "\n", "print(\"\\n   ⚖️ Berdasarkan Kategori BMI:\")\n", "for bmi_cat in bmi_stunting.index:\n", "    stunting_count = bmi_stunting.loc[bmi_cat, 'Stunting']\n", "    total_count = bmi_stunting.loc[bmi_cat, 'Total']\n", "    percentage = bmi_stunting.loc[bmi_cat, 'Persentase_Stunting']\n", "    print(f\"   {bmi_cat}: {stunting_count}/{total_count} balita ({percentage}%)\")\n", "\n", "print(\"\\n✅ Exploratory Data Analysis selesai!\")\n", "print(\"📊 Insights awal telah diperoleh untuk analisis lanjutan\")"], "metadata": {"id": "eda_analysis"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## **🤖 Langkah 5: Persiapan Data untuk Machine Learning**\n", "\n", "**Tujuan:** Mempersiapkan data untuk training model machine learning dengan encoding dan scaling yang tepat\n", "\n", "**Proses yang di<PERSON>n:**\n", "- Encoding variabel kategorikal menjadi numerik\n", "- <PERSON><PERSON><PERSON><PERSON> fitur yang relevan untuk model\n", "- Split data training dan testing (80:20)\n", "- Standardisasi fitur numerik untuk performa model yang optimal"], "metadata": {"id": "step5_header"}}, {"cell_type": "code", "source": ["print(\"🤖 Mempersiapkan Data untuk Machine Learning...\")\n", "print(\"=\"*50)\n", "\n", "# Buat copy dataset untuk machine learning\n", "df_ml = df_clean.copy()\n", "\n", "# 1. Encoding variabel kategorikal menjadi numerik\n", "print(\"\\n1️⃣ Encoding Variabel Kategorikal:\")\n", "\n", "# Label Encoder untuk variabel kategorikal\n", "le_gender = LabelEncoder()\n", "le_region = LabelEncoder()\n", "le_age_cat = LabelEncoder()\n", "le_bmi_cat = LabelEncoder()\n", "le_status = LabelEncoder()\n", "\n", "# Encoding semua variabel kate<PERSON>l\n", "df_ml['jenis_kelamin_encoded'] = le_gender.fit_transform(df_ml['jenis_kelamin'])\n", "df_ml['kabupaten_encoded'] = le_region.fit_transform(df_ml['nama_kabupaten_kota'])\n", "df_ml['kategori_usia_encoded'] = le_age_cat.fit_transform(df_ml['kategori_usia'])\n", "df_ml['kategori_bmi_encoded'] = le_bmi_cat.fit_transform(df_ml['kategori_bmi'])\n", "df_ml['status_encoded'] = le_status.fit_transform(df_ml['status'])\n", "\n", "print(f\"   ✅ <PERSON><PERSON> kelamin: {df_ml['jenis_kelamin'].nunique()} kate<PERSON>i → encoded\")\n", "print(f\"   ✅ Kabupaten/kota: {df_ml['nama_kabupaten_kota'].nunique()} kategori → encoded\")\n", "print(f\"   ✅ Kategori usia: {df_ml['kategori_usia'].nunique()} kategori → encoded\")\n", "print(f\"   ✅ Kategori BMI: {df_ml['kategori_bmi'].nunique()} kategori → encoded\")\n", "print(f\"   ✅ Status: {df_ml['status'].nunique()} kategori → encoded\")\n", "\n", "# 2. <PERSON><PERSON><PERSON><PERSON> fitur yang relevan untuk model\n", "print(\"\\n2️⃣ Pemilihan Fitur untuk Model:\")\n", "feature_columns = [\n", "    'tahun', 'tinggi_badan_cm', 'berat_badan_kg', 'usia_balita_bulan', 'bmi',\n", "    'jenis_k<PERSON>min_encoded', 'kabupaten_encoded', 'kategori_usia_encoded', 'kategori_bmi_encoded'\n", "]\n", "\n", "X = df_ml[feature_columns]\n", "y = df_ml['status_encoded']\n", "\n", "print(f\"   ✅ Jumlah fitur: {len(feature_columns)}\")\n", "print(f\"   ✅ Fitur yang digunakan: {feature_columns}\")\n", "print(f\"   ✅ Target variable: status (0=Non-Stunting, 1=Stunting)\")\n", "\n", "# 3. Split data training dan testing (80:20)\n", "print(\"\\n3️⃣ Split Data Training dan Testing:\")\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)\n", "\n", "print(f\"   ✅ Data training: {X_train.shape[0]} sampel ({X_train.shape[0]/len(X)*100:.1f}%)\")\n", "print(f\"   ✅ Data testing: {X_test.shape[0]} sampel ({X_test.shape[0]/len(X)*100:.1f}%)\")\n", "print(f\"   ✅ Distribusi kelas training: {np.bincount(y_train)}\")\n", "print(f\"   ✅ Distribusi kelas testing: {np.bincount(y_test)}\")\n", "\n", "# 4. Standardisasi fitur numerik untuk performa optimal\n", "print(\"\\n4️⃣ Standardisasi Fitur Numerik:\")\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "print(f\"   ✅ Fitur numerik telah distandardisasi\")\n", "print(f\"   ✅ Mean setelah scaling: {X_train_scaled.mean():.6f}\")\n", "print(f\"   ✅ Std setelah scaling: {X_train_scaled.std():.6f}\")\n", "\n", "print(\"\\n✅ Persiapan data untuk ML selesai!\")\n", "print(f\"📊 Siap untuk training 3 model: Decision Tree, KNN, <PERSON><PERSON>\")"], "metadata": {"id": "ml_preparation"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## **🎯 Langkah 6: Implementasi dan Training 3 Model Machine Learning**\n", "\n", "**Tujuan:** Melatih dan mengevaluasi 3 model ML untuk memilih yang paling efisien berdasarkan akurasi dan kecepatan\n", "\n", "**Model yang digunakan:**\n", "- **Decision Tree Classifier**: Model berbasis pohon keputusan yang mudah diinterpretasi\n", "- **K-Nearest Neighbors (KNN)**: Model berb<PERSON> kemiripan dengan tetangga terdekat\n", "- **<PERSON><PERSON><PERSON>**: Model probabilistik berbasis teorema <PERSON>\n", "\n", "**<PERSON><PERSON> eval<PERSON>:**\n", "- Accuracy, Precision, Recall, F1-Score\n", "- Waktu training (untuk efisiensi)\n", "- Confusion Matrix"], "metadata": {"id": "step6_header"}}, {"cell_type": "code", "source": ["print(\"🎯 Training dan Evalu<PERSON> 3 Model Machine Learning...\")\n", "print(\"=\"*70)\n", "\n", "# Dictionary untuk menyimpan hasil semua model\n", "models_results = {}\n", "\n", "# 1. DECISION TREE CLASSIFIER\n", "print(\"\\n1️⃣ TRAINING DECISION TREE CLASSIFIER\")\n", "print(\"-\" * 50)\n", "\n", "start_time = time.time()\n", "dt_model = DecisionTreeClassifier(random_state=42, max_depth=10, min_samples_split=5)\n", "dt_model.fit(X_train_scaled, y_train)\n", "dt_train_time = time.time() - start_time\n", "\n", "# Prediksi dan evaluasi\n", "dt_pred = dt_model.predict(X_test_scaled)\n", "dt_accuracy = accuracy_score(y_test, dt_pred)\n", "dt_precision = precision_score(y_test, dt_pred, average='weighted')\n", "dt_recall = recall_score(y_test, dt_pred, average='weighted')\n", "dt_f1 = f1_score(y_test, dt_pred, average='weighted')\n", "\n", "models_results['Decision Tree'] = {\n", "    'model': dt_model,\n", "    'predictions': dt_pred,\n", "    'accuracy': dt_accuracy,\n", "    'precision': dt_precision,\n", "    'recall': dt_recall,\n", "    'f1_score': dt_f1,\n", "    'training_time': dt_train_time\n", "}\n", "\n", "print(f\"   ✅ Training selesai dalam {dt_train_time:.4f} detik\")\n", "print(f\"   📊 Accuracy: {dt_accuracy:.4f} ({dt_accuracy*100:.2f}%)\")\n", "print(f\"   📊 Precision: {dt_precision:.4f}\")\n", "print(f\"   📊 Recall: {dt_recall:.4f}\")\n", "print(f\"   📊 F1-Score: {dt_f1:.4f}\")\n", "\n", "# 2. K-NEAREST NEIGHBORS (KNN)\n", "print(\"\\n2️⃣ TRAINING K-NEAREST NEIGHBORS (KNN)\")\n", "print(\"-\" * 50)\n", "\n", "start_time = time.time()\n", "knn_model = KNeighborsClassifier(n_neighbors=5, weights='distance')\n", "knn_model.fit(X_train_scaled, y_train)\n", "knn_train_time = time.time() - start_time\n", "\n", "# Prediksi dan evaluasi\n", "knn_pred = knn_model.predict(X_test_scaled)\n", "knn_accuracy = accuracy_score(y_test, knn_pred)\n", "knn_precision = precision_score(y_test, knn_pred, average='weighted')\n", "knn_recall = recall_score(y_test, knn_pred, average='weighted')\n", "knn_f1 = f1_score(y_test, knn_pred, average='weighted')\n", "\n", "models_results['KNN'] = {\n", "    'model': knn_model,\n", "    'predictions': knn_pred,\n", "    'accuracy': knn_accuracy,\n", "    'precision': knn_precision,\n", "    'recall': knn_recall,\n", "    'f1_score': knn_f1,\n", "    'training_time': knn_train_time\n", "}\n", "\n", "print(f\"   ✅ Training selesai dalam {knn_train_time:.4f} detik\")\n", "print(f\"   📊 Accuracy: {knn_accuracy:.4f} ({knn_accuracy*100:.2f}%)\")\n", "print(f\"   📊 Precision: {knn_precision:.4f}\")\n", "print(f\"   📊 Recall: {knn_recall:.4f}\")\n", "print(f\"   📊 F1-Score: {knn_f1:.4f}\")\n", "\n", "# 3. GAUSSIAN NAIVE BAYES\n", "print(\"\\n3️⃣ TRAINING GAUSSIAN NAIVE BAYES\")\n", "print(\"-\" * 50)\n", "\n", "start_time = time.time()\n", "nb_model = GaussianNB()\n", "nb_model.fit(X_train_scaled, y_train)\n", "nb_train_time = time.time() - start_time\n", "\n", "# Prediksi dan evaluasi\n", "nb_pred = nb_model.predict(X_test_scaled)\n", "nb_accuracy = accuracy_score(y_test, nb_pred)\n", "nb_precision = precision_score(y_test, nb_pred, average='weighted')\n", "nb_recall = recall_score(y_test, nb_pred, average='weighted')\n", "nb_f1 = f1_score(y_test, nb_pred, average='weighted')\n", "\n", "models_results['Na<PERSON>'] = {\n", "    'model': nb_model,\n", "    'predictions': nb_pred,\n", "    'accuracy': nb_accuracy,\n", "    'precision': nb_precision,\n", "    'recall': nb_recall,\n", "    'f1_score': nb_f1,\n", "    'training_time': nb_train_time\n", "}\n", "\n", "print(f\"   ✅ Training selesai dalam {nb_train_time:.4f} detik\")\n", "print(f\"   📊 Accuracy: {nb_accuracy:.4f} ({nb_accuracy*100:.2f}%)\")\n", "print(f\"   📊 Precision: {nb_precision:.4f}\")\n", "print(f\"   📊 Recall: {nb_recall:.4f}\")\n", "print(f\"   📊 F1-Score: {nb_f1:.4f}\")\n", "\n", "print(\"\\n✅ Training semua model selesai!\")\n", "print(\"📊 Siap untuk evaluasi dan pemilihan model terbaik\")"], "metadata": {"id": "ml_training"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## **📊 Langkah 7: <PERSON><PERSON><PERSON><PERSON> Terbaik**\n", "\n", "**Tujuan:** Membandingkan performa ketiga model dan memilih yang paling efisien berdasarkan akurasi dan kecepatan\n", "\n", "**<PERSON><PERSON><PERSON> pem<PERSON>:**\n", "- F1-Score (prioritas utama untuk balanced performance)\n", "- Accuracy (akura<PERSON> prediksi)\n", "- Training Time (efisiensi waktu)\n", "- Precision dan <PERSON> (untuk analisis detail)"], "metadata": {"id": "step7_header"}}, {"cell_type": "code", "source": ["print(\"📊 Eva<PERSON><PERSON> da<PERSON> Model Terbaik...\")\n", "print(\"=\"*70)\n", "\n", "# 1. <PERSON><PERSON> performa semua model\n", "print(\"\\n1️⃣ PERBANDINGAN PERFORMA MODEL\")\n", "print(\"-\" * 50)\n", "\n", "comparison_data = []\n", "for model_name, results in models_results.items():\n", "    comparison_data.append([\n", "        model_name,\n", "        f\"{results['accuracy']:.4f}\",\n", "        f\"{results['precision']:.4f}\",\n", "        f\"{results['recall']:.4f}\",\n", "        f\"{results['f1_score']:.4f}\",\n", "        f\"{results['training_time']:.4f}s\"\n", "    ])\n", "\n", "headers = ['Model', 'Accuracy', 'Precision', 'Recall', 'F1-Score', 'Training Time']\n", "print(tabulate(comparison_data, headers=headers, tablefmt='grid', stralign='center'))\n", "\n", "# 2. <PERSON><PERSON><PERSON><PERSON> model <PERSON><PERSON><PERSON><PERSON>-Score\n", "print(\"\\n2️⃣ PEMILIHAN MODEL TERBAIK\")\n", "print(\"-\" * 40)\n", "\n", "best_model_name = max(models_results.keys(), key=lambda x: models_results[x]['f1_score'])\n", "best_model = models_results[best_model_name]['model']\n", "best_predictions = models_results[best_model_name]['predictions']\n", "\n", "print(f\"🏆 MODEL TERBAIK: {best_model_name}\")\n", "print(f\"   📊 F1-Score: {models_results[best_model_name]['f1_score']:.4f}\")\n", "print(f\"   📊 Accuracy: {models_results[best_model_name]['accuracy']:.4f} ({models_results[best_model_name]['accuracy']*100:.2f}%)\")\n", "print(f\"   ⚡ Training Time: {models_results[best_model_name]['training_time']:.4f} detik\")\n", "\n", "# 3. <PERSON><PERSON><PERSON> model\n", "print(\"\\n3️⃣ ALASAN PEMILIHAN MODEL\")\n", "print(\"-\" * 30)\n", "print(f\"✅ {best_model_name} dipilih karena:\")\n", "print(f\"   • F1-Score tertinggi: {models_results[best_model_name]['f1_score']:.4f}\")\n", "print(f\"   • Balanced performance antara precision dan recall\")\n", "print(f\"   • Waktu training yang efisien: {models_results[best_model_name]['training_time']:.4f} detik\")\n", "print(f\"   • Cocok untuk dataset stunting dengan class imbalance\")\n", "\n", "# 4. Ranking semua model\n", "print(\"\\n4️⃣ RANKING MODEL BERDASARKAN F1-SCORE\")\n", "print(\"-\" * 40)\n", "sorted_models = sorted(models_results.items(), key=lambda x: x[1]['f1_score'], reverse=True)\n", "for i, (model_name, results) in enumerate(sorted_models, 1):\n", "    print(f\"   {i}. {model_name}: F1-Score = {results['f1_score']:.4f}\")\n", "\n", "print(\"\\n✅ Eva<PERSON>asi dan pemilihan model terbaik selesai!\")\n", "print(f\"🎯 Model {best_model_name} akan digunakan untuk analisis lanjutan\")"], "metadata": {"id": "model_evaluation"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## **🎯 Langka<PERSON> 8: <PERSON><PERSON>is Confusion Matrix dan Metrics Detail**\n", "\n", "**Tujuan:** Menganalisis detail performa model terbaik melalui confusion matrix dan classification report\n", "\n", "**<PERSON><PERSON><PERSON> yang <PERSON>:**\n", "- Confusion Matrix untuk semua model\n", "- Classification Report detail untuk model terbaik\n", "- Interpretasi hasil prediksi dan error analysis\n", "- Sensitivity dan Specificity analysis"], "metadata": {"id": "step8_header"}}, {"cell_type": "code", "source": ["print(\"🎯 Analisis Confusion Matrix dan Classification Report...\")\n", "print(\"=\"*70)\n", "\n", "# 1. Confusion Matrix untuk semua model\n", "print(\"\\n1️⃣ CONFUSION MATRIX UNTUK SEMUA MODEL\")\n", "print(\"-\" * 50)\n", "\n", "# Visualisasi confusion matrix untuk semua model\n", "fig, axes = plt.subplots(1, 3, figsize=(18, 5))\n", "fig.suptitle('🎯 Confusion Matrix - Perbandingan 3 Model ML', fontsize=16, fontweight='bold')\n", "\n", "model_names = list(models_results.keys())\n", "for i, (model_name, results) in enumerate(models_results.items()):\n", "    cm = confusion_matrix(y_test, results['predictions'])\n", "\n", "    # Plot confusion matrix\n", "    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=axes[i],\n", "                xticklabels=['Non-Stunting', 'Stunting'],\n", "                yticklabels=['Non-Stunting', 'Stunting'])\n", "    axes[i].set_title(f'{model_name}\\nAccuracy: {results[\"accuracy\"]:.3f}', fontweight='bold')\n", "    axes[i].set_xlabel('Prediksi')\n", "    axes[i].set_ylabel('Aktual')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 2. Classification Report untuk model terbaik\n", "print(f\"\\n2️⃣ CLASSIFICATION REPORT - {best_model_name.upper()}\")\n", "print(\"-\" * 50)\n", "\n", "class_report = classification_report(y_test, best_predictions, output_dict=True)\n", "print(classification_report(y_test, best_predictions, target_names=['Non-Stunting', 'Stunting']))\n", "\n", "# Tabel classification report yang lebih rapi\n", "report_data = []\n", "for class_name in ['Non-Stunting', 'Stunting']:\n", "    class_key = '0' if class_name == 'Non-Stunting' else '1'\n", "    report_data.append([\n", "        class_name,\n", "        f\"{class_report[class_key]['precision']:.4f}\",\n", "        f\"{class_report[class_key]['recall']:.4f}\",\n", "        f\"{class_report[class_key]['f1-score']:.4f}\",\n", "        f\"{int(class_report[class_key]['support'])}\"\n", "    ])\n", "\n", "# Tambahkan weighted average\n", "report_data.append([\n", "    'Weighted Avg',\n", "    f\"{class_report['weighted avg']['precision']:.4f}\",\n", "    f\"{class_report['weighted avg']['recall']:.4f}\",\n", "    f\"{class_report['weighted avg']['f1-score']:.4f}\",\n", "    f\"{int(class_report['weighted avg']['support'])}\"\n", "])\n", "\n", "headers = ['<PERSON><PERSON>', 'Precision', 'Recall', 'F1-Score', 'Support']\n", "print(tabulate(report_data, headers=headers, tablefmt='grid', stralign='center'))\n", "\n", "# 3. Interpretasi hasil confusion matrix model terb<PERSON>k\n", "print(f\"\\n3️⃣ INTERPRETASI HASIL {best_model_name.upper()}\")\n", "print(\"-\" * 50)\n", "\n", "cm_best = confusion_matrix(y_test, best_predictions)\n", "tn, fp, fn, tp = cm_best.ravel()\n", "\n", "print(f\"📊 Confusion Matrix Breakdown:\")\n", "print(f\"   ✅ True Negative (TN): {tn} - Non-stunting diprediksi benar\")\n", "print(f\"   ❌ False Positive (FP): {fp} - Non-stunting diprediksi stunting\")\n", "print(f\"   ❌ False Negative (FN): {fn} - Stunting diprediksi non-stunting\")\n", "print(f\"   ✅ True Positive (TP): {tp} - Stunting diprediksi benar\")\n", "\n", "print(f\"\\n📈 <PERSON>:\")\n", "sensitivity = tp / (tp + fn)  # Recall untuk kelas <PERSON>unting\n", "specificity = tn / (tn + fp)  # Recall untuk kelas Non-Stunting\n", "print(f\"   • Sensitivity (Recall Stunting): {sensitivity:.4f} ({sensitivity*100:.1f}%)\")\n", "print(f\"   • Specificity (Recall Non-Stunting): {specificity:.4f} ({specificity*100:.1f}%)\")\n", "\n", "print(f\"\\n💡 Interpretasi Klinis:\")\n", "print(f\"   • Model dapat mendeteksi {sensitivity*100:.1f}% kasus stunting dengan benar\")\n", "print(f\"   • Model dapat mengidentifikasi {specificity*100:.1f}% kasus non-stunting dengan benar\")\n", "print(f\"   • Tingkat kesalahan prediksi: {(fp+fn)/(tn+fp+fn+tp)*100:.1f}%\")\n", "\n", "print(\"\\n✅ Analisis confusion matrix selesai!\")\n", "print(\"📊 Model siap untuk analisis regional dan prediksi\")"], "metadata": {"id": "confusion_matrix_analysis"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## **🗺️ Langkah 9: Analisis Regional Kabupaten/Kota Jawa Barat**\n", "\n", "**Tujuan:** Menganalisis tingkat stunting per kabupaten/kota di Jawa Barat untuk identifikasi daerah prioritas intervensi\n", "\n", "**<PERSON><PERSON><PERSON> yang <PERSON>:**\n", "- Ranking kabupaten/kota berdasarkan persentase stunting\n", "- <PERSON><PERSON><PERSON><PERSON><PERSON> da<PERSON>h dengan stunting tertinggi dan terendah\n", "- <PERSON><PERSON><PERSON> balita per daerah\n", "- Kategorisasi risiko dan rekomendasi intervensi"], "metadata": {"id": "step9_header"}}, {"cell_type": "code", "source": ["print(\"🗺️ Analisis Regional Stunting Kabupaten/Kota Jawa Barat...\")\n", "print(\"=\"*80)\n", "\n", "# 1. <PERSON><PERSON><PERSON> per kabupaten/kota\n", "print(\"\\n1️⃣ ANALISIS KOMPREHENSIF PER KABUPATEN/KOTA\")\n", "print(\"-\" * 60)\n", "\n", "# Hitung statistik detail per daerah menggunakan semua field dataset\n", "regional_detailed = df_clean.groupby('nama_kabupaten_kota').agg({\n", "    'status': ['count', lambda x: (x == 'Stunting').sum()],\n", "    'tinggi_badan_cm': ['mean', 'std'],\n", "    'berat_badan_kg': ['mean', 'std'],\n", "    'usia_balita_bulan': ['mean', 'std'],\n", "    'bmi': ['mean', 'std'],\n", "    'jenis_kelamin': lambda x: (x == 'Laki-laki').sum(),\n", "    'tahun': lambda x: x.mode().iloc[0] if len(x.mode()) > 0 else x.iloc[0]\n", "}).round(2)\n", "\n", "# Flatten column names\n", "regional_detailed.columns = [\n", "    'Total_Balita', '<PERSON><PERSON><PERSON>_<PERSON>unting', '<PERSON><PERSON>_Tinggi', 'Std_Tinggi',\n", "    '<PERSON><PERSON>_<PERSON><PERSON>', '<PERSON><PERSON>_<PERSON>rat', '<PERSON><PERSON>_<PERSON><PERSON>', 'Std_<PERSON><PERSON>',\n", "    '<PERSON><PERSON>_<PERSON><PERSON>', '<PERSON>d_B<PERSON>', '<PERSON><PERSON><PERSON>_<PERSON><PERSON>', '<PERSON><PERSON>_<PERSON>inan'\n", "]\n", "\n", "# Hitung persentase dan tambahan metrics\n", "regional_detailed['Persentase_Stunting'] = (regional_detailed['Ju<PERSON>lah_Stunting'] / regional_detailed['Total_Balita'] * 100).round(2)\n", "regional_detailed['Persentase_<PERSON><PERSON>'] = (regional_detailed['<PERSON><PERSON><PERSON>_<PERSON><PERSON>'] / regional_detailed['Total_Balita'] * 100).round(2)\n", "\n", "# <PERSON><PERSON><PERSON><PERSON> risiko be<PERSON> persentase stunting\n", "def kate<PERSON><PERSON>n_risiko(pct):\n", "    if pct >= 30:\n", "        return '<PERSON><PERSON><PERSON>'\n", "    elif pct >= 20:\n", "        return 'Sedang'\n", "    else:\n", "        return 'Rendah'\n", "\n", "regional_detailed['Kategori_Risiko'] = regional_detailed['Persentase_Stunting'].apply(kategorikan_risiko)\n", "\n", "# Sort berdasarkan persentase stunting\n", "regional_detailed = regional_detailed.sort_values('Persentase_Stunting', ascending=False)\n", "\n", "print(f\"📊 Total kabupaten/kota dianalisis: {len(regional_detailed)}\")\n", "print(f\"📊 Rata-rata stunting <PERSON><PERSON>at: {regional_detailed['Persentase_Stunting'].mean():.2f}%\")\n", "print(f\"📊 Standar deviasi: {regional_detailed['Persentase_Stunting'].std():.2f}%\")\n", "\n", "# 2. Top 10 kabupaten/kota dengan stunting tertinggi\n", "print(\"\\n2️⃣ TOP 10 KABUPATEN/KOTA STUNTING TERTINGGI\")\n", "print(\"-\" * 60)\n", "\n", "highest_data = []\n", "for i, (region, data) in enumerate(regional_detailed.head(10).iterrows(), 1):\n", "    highest_data.append([\n", "        i,\n", "        region[:25] + '...' if len(region) > 25 else region,\n", "        f\"{data['Persentase_Stunting']:.1f}%\",\n", "        f\"{int(data['<PERSON><PERSON><PERSON>_Stunting'])}/{int(data['Total_Balita'])}\",\n", "        f\"{data['<PERSON><PERSON>_<PERSON>ggi']:.1f} cm\",\n", "        f\"{data['Rata_BMI']:.1f}\",\n", "        data['<PERSON><PERSON><PERSON>_R<PERSON><PERSON>']\n", "    ])\n", "\n", "headers_highest = ['Rank', 'Kabupaten/Kota', 'Stunting %', 'Stunting/Total', 'Rata Tinggi', 'Rata BMI', 'R<PERSON><PERSON>']\n", "print(tabulate(highest_data, headers=headers_highest, tablefmt='grid', stralign='center'))\n", "\n", "# 3. Top 10 kabupaten/kota dengan stunting terendah\n", "print(\"\\n3️⃣ TOP 10 KABUPATEN/KOTA STUNTING TERENDAH\")\n", "print(\"-\" * 60)\n", "\n", "lowest_data = []\n", "for i, (region, data) in enumerate(regional_detailed.tail(10).iterrows(), 1):\n", "    lowest_data.append([\n", "        i,\n", "        region[:25] + '...' if len(region) > 25 else region,\n", "        f\"{data['Persentase_Stunting']:.1f}%\",\n", "        f\"{int(data['<PERSON><PERSON><PERSON>_Stunting'])}/{int(data['Total_Balita'])}\",\n", "        f\"{data['<PERSON><PERSON>_<PERSON>ggi']:.1f} cm\",\n", "        f\"{data['Rata_BMI']:.1f}\",\n", "        data['<PERSON><PERSON><PERSON>_R<PERSON><PERSON>']\n", "    ])\n", "\n", "headers_lowest = ['Rank', 'Kabupaten/Kota', 'Stunting %', 'Stunting/Total', 'Rata Tinggi', 'Rata BMI', 'R<PERSON><PERSON>']\n", "print(tabulate(lowest_data, headers=headers_lowest, tablefmt='grid', stralign='center'))\n", "\n", "# 4. <PERSON><PERSON><PERSON><PERSON> r<PERSON>\n", "print(\"\\n4️⃣ KATEGORISASI RISIKO STUNTING\")\n", "print(\"-\" * 40)\n", "\n", "risk_summary = regional_detailed['Kategori_Risiko'].value_counts()\n", "for risk_level in ['Tinggi', 'Sedang', 'Rendah']:\n", "    if risk_level in risk_summary.index:\n", "        count = risk_summary[risk_level]\n", "        pct = (count / len(regional_detailed) * 100)\n", "        icon = '🔴' if risk_level == 'Tinggi' else '🟡' if risk_level == 'Sedang' else '🟢'\n", "        print(f\"   {icon} R<PERSON>ko {risk_level}: {count} daerah ({pct:.1f}%)\")\n", "\n", "        # <PERSON><PERSON><PERSON><PERSON> beberapa contoh daerah\n", "        regions = regional_detailed[regional_detailed['Kategori_Risiko'] == risk_level].index[:5]\n", "        for region in regions:\n", "            pct = regional_detailed.loc[region, 'Persentase_Stunting']\n", "            print(f\"   • {region}: {pct:.1f}%\")\n", "        if len(regions) > 5:\n", "            print(f\"   • ... dan {len(regions)-5} daerah lainnya\")\n", "\n", "print(\"\\n✅ Analisis regional stunting selesai!\")\n", "print(\"📊 Data siap untuk tabel analisis komprehensif\")"], "metadata": {"id": "regional_analysis"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## **📋 Langkah 10: <PERSON><PERSON>**\n", "\n", "**Tujuan:** Membuat tabel analisis detail dengan agregasi data yang komprehensif\n", "\n", "**Format tabel mengikuti struktur:**\n", "- Agregasi berdasarkan kabupaten/kota dan tahun\n", "- Statistik deskriptif lengkap (mean, median, std, min, max)\n", "- <PERSON><PERSON><PERSON> distribusi stunting per karakteristik\n", "- Tabel summary yang mudah dipahami dan actionable"], "metadata": {"id": "step10_header"}}, {"cell_type": "code", "source": ["print(\"📋 JABAR STUNTING ANALYZER - Membuat Tabel Analisis <PERSON>...\")\n", "print(\"=\"*70)\n", "\n", "# 1. TABEL AGREGASI UTAMA - Format mengikuti referensi\n", "print(\"\\n1️⃣ TABEL AGREGASI STUNTING PER KABUPATEN/KOTA\")\n", "print(\"-\" * 60)\n", "\n", "# Buat tabel agregasi yang mengikuti format referensi\n", "tabel_agregasi = df_clean.groupby(['nama_kabupaten_kota', 'tahun']).agg({\n", "    'status': ['count', lambda x: (x == 'Stunting').sum()],\n", "    'tinggi_badan_cm': ['mean', 'median', 'std', 'min', 'max'],\n", "    'berat_badan_kg': ['mean', 'median', 'std', 'min', 'max'],\n", "    'usia_balita_bulan': ['mean', 'median', 'std'],\n", "    'bmi': ['mean', 'median', 'std'],\n", "    'jenis_kelamin': lambda x: (x == 'Laki-laki').sum()\n", "}).round(2)\n", "\n", "# Flatten dan rename columns untuk kem<PERSON>han\n", "tabel_agregasi.columns = [\n", "    'Total_Balita', '<PERSON><PERSON>_Count', '<PERSON>gg<PERSON>_<PERSON>', 'Tingg<PERSON>_Median', 'Tinggi_Std', '<PERSON>gg<PERSON>_<PERSON>', 'Tingg<PERSON>_<PERSON>',\n", "    '<PERSON><PERSON>_<PERSON>', '<PERSON><PERSON>_Median', '<PERSON>rat_Std', '<PERSON><PERSON>_<PERSON>', '<PERSON>rat_<PERSON>',\n", "    '<PERSON><PERSON>_Mean', '<PERSON><PERSON>_Median', '<PERSON>ia_Std', 'BMI_Mean', 'BMI_Median', 'BMI_Std', '<PERSON><PERSON>_Count'\n", "]\n", "\n", "# Tambahkan kolom perhitungan\n", "tabel_agregasi['Stunting_Pct'] = (tabel_agregasi['Stunting_Count'] / tabel_agregasi['Total_Balita'] * 100).round(2)\n", "tabel_agregasi['Laki_Pct'] = (tabel_agregasi['<PERSON><PERSON>_Count'] / tabel_agregasi['Total_Balita'] * 100).round(2)\n", "tabel_agregasi['Non_Stunting_Count'] = tabel_agregasi['Total_Balita'] - tabel_agregasi['Stunting_Count']\n", "\n", "# Reset index untuk tampilan yang lebih baik\n", "tabel_agregasi = tabel_agregasi.reset_index()\n", "\n", "# Tam<PERSON>lkan 15 baris pertama dari tabel agregasi\n", "print(\"📊 <PERSON><PERSON>h <PERSON> (15 baris pertama):\")\n", "display_cols = ['nama_kabupaten_kota', 'tahun', 'Total_Balita', 'Stunting_Count', 'Stunting_Pct',\n", "                '<PERSON><PERSON><PERSON>_Mean', '<PERSON><PERSON>_Mean', 'B<PERSON>_Mean']\n", "print(tabulate(tabel_agregasi[display_cols].head(15), headers=display_cols, tablefmt='grid', floatfmt='.2f'))\n", "\n", "# 2. TABEL SUMMARY STATISTIK DESKRIPTIF\n", "print(\"\\n2️⃣ TABEL SUMMARY STATISTIK DESKRIPTIF\")\n", "print(\"-\" * 50)\n", "\n", "# Buat summary statistik untuk semua variabel numerik\n", "summary_stats = df_clean[['tinggi_badan_cm', 'berat_badan_kg', 'usia_balita_bulan', 'bmi']].describe().round(2)\n", "summary_stats.loc['variance'] = df_clean[['tinggi_badan_cm', 'berat_badan_kg', 'usia_balita_bulan', 'bmi']].var().round(2)\n", "\n", "print(\"📊 Statistik Deskriptif Variabel Numerik:\")\n", "print(tabulate(summary_stats, headers=['Tinggi (cm)', 'Berat (kg)', 'Usia (bulan)', 'BMI'], tablefmt='grid', floatfmt='.2f'))\n", "\n", "# 3. TABEL DISTRIBUSI STUNTING PER KARAKTERISTIK\n", "print(\"\\n3️⃣ TABEL DISTRIBUSI STUNTING PER KARAKTERISTIK\")\n", "print(\"-\" * 55)\n", "\n", "# Distribusi be<PERSON> jenis kelamin\n", "gender_table = pd.crosstab(df_clean['jenis_kelamin'], df_clean['status'], margins=True)\n", "gender_table['Stunting_Pct'] = (gender_table['Stunting'] / gender_table['All'] * 100).round(2)\n", "\n", "print(\"👶 Distribusi Stunting berdasarkan Jen<PERSON>:\")\n", "print(tabulate(gender_table, headers=['Non-Stunting', 'Stunting', 'Total', 'Stunting %'], tablefmt='grid'))\n", "\n", "# Distribusi berdasarkan kategori usia\n", "age_table = pd.crosstab(df_clean['kategori_usia'], df_clean['status'], margins=True)\n", "age_table['Stunting_Pct'] = (age_table['Stunting'] / age_table['All'] * 100).round(2)\n", "\n", "print(\"\\n📅 Distribusi Stunting berdasarkan Kategori Usia:\")\n", "print(tabulate(age_table, headers=['Non-Stunting', 'Stunting', 'Total', 'Stunting %'], tablefmt='grid'))\n", "\n", "# Distribusi berdasarkan kategori BMI\n", "bmi_table = pd.crosstab(df_clean['kategori_bmi'], df_clean['status'], margins=True)\n", "bmi_table['Stunting_Pct'] = (bmi_table['Stunting'] / bmi_table['All'] * 100).round(2)\n", "\n", "print(\"\\n⚖️ Distribusi Stunting berdasarkan Kategori BMI:\")\n", "print(tabulate(bmi_table, headers=['Non-Stunting', 'Stunting', 'Total', 'Stunting %'], tablefmt='grid'))\n", "\n", "# 4. TABEL RANKING DAERAH PRIORITAS\n", "print(\"\\n4️⃣ TABEL RANKING DAERAH PRIORITAS INTERVENSI\")\n", "print(\"-\" * 55)\n", "\n", "# Ambil data dari analisis regional sebelumnya\n", "priority_table = regional_detailed[['Total_Balita', 'Ju<PERSON>lah_Stunting', 'Persentase_Stunting',\n", "                                   '<PERSON><PERSON>_<PERSON><PERSON><PERSON>', '<PERSON><PERSON>_<PERSON><PERSON>', '<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>']].head(15)\n", "\n", "priority_data = []\n", "for i, (region, data) in enumerate(priority_table.iterrows(), 1):\n", "    priority_data.append([\n", "        i,\n", "        region[:30] + '...' if len(region) > 30 else region,\n", "        int(data['Total_Balita']),\n", "        int(data['<PERSON><PERSON><PERSON>_Stunting']),\n", "        f\"{data['Persentase_Stunting']:.1f}%\",\n", "        f\"{data['<PERSON><PERSON>_<PERSON>ggi']:.1f}\",\n", "        f\"{data['Rata_BMI']:.1f}\",\n", "        data['<PERSON><PERSON><PERSON>_R<PERSON><PERSON>']\n", "    ])\n", "\n", "headers_priority = ['Rank', 'Kabupaten/Kota', 'Total', 'Stunting', 'Stunting %', 'Rata Tinggi', 'Rata BMI', '<PERSON><PERSON><PERSON>']\n", "print(\"🎯 Top 15 Daerah Prioritas Intervensi:\")\n", "print(tabulate(priority_data, headers=headers_priority, tablefmt='grid', stralign='center'))\n", "\n", "print(\"\\n✅ Tabel analisis komprehensif selesai!\")\n", "print(\"📊 Semua tabel mengikuti format referensi dengan agregasi yang tepat\")\n", "\n", "# 5. EXPORT TABEL KE EXCEL\n", "print(\"\\n5️⃣ EXPORT TABEL ANALISIS KE EXCEL\")\n", "print(\"-\" * 50)\n", "\n", "try:\n", "    from datetime import datetime\n", "    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "    filename = f'JABAR_STUNTING_ANALYZER_{timestamp}.xlsx'\n", "\n", "    # Buat Excel writer object\n", "    with pd.ExcelWriter(filename, engine='openpyxl') as writer:\n", "\n", "        # Sheet 1: Regional Analysis\n", "        regional_export = regional_detailed.copy()\n", "        regional_export.reset_index(inplace=True)\n", "        regional_export.to_excel(writer, sheet_name='Regional_Analysis', index=False)\n", "\n", "        # Sheet 2: Aggregation Table\n", "        tabel_agregasi.to_excel(writer, sheet_name='Aggregation_Table', index=True)\n", "\n", "        # Sheet 3: Summary Statistics\n", "        summary_stats.to_excel(writer, sheet_name='Summary_Statistics', index=True)\n", "\n", "        # Sheet 4: Distribution Tables\n", "        gender_table.to_excel(writer, sheet_name='Gender_Distribution', index=True)\n", "        age_table.to_excel(writer, sheet_name='Age_Distribution', index=True)\n", "        bmi_table.to_excel(writer, sheet_name='BMI_Distribution', index=True)\n", "\n", "        # Sheet 5: Model Results\n", "        model_results_df = pd.DataFrame({\n", "            'Model': list(models_results.keys()),\n", "            'Accuracy': [results['accuracy'] for results in models_results.values()],\n", "            'Precision': [results['precision'] for results in models_results.values()],\n", "            'Recall': [results['recall'] for results in models_results.values()],\n", "            'F1_Score': [results['f1_score'] for results in models_results.values()],\n", "            'Training_Time': [results['training_time'] for results in models_results.values()]\n", "        })\n", "        model_results_df.to_excel(writer, sheet_name='Model_Results', index=False)\n", "\n", "        # Sheet 6: Priority Ranking\n", "        priority_df = pd.DataFrame(priority_data, columns=headers_priority)\n", "        priority_df.to_excel(writer, sheet_name='Priority_Ranking', index=False)\n", "\n", "    print(f\"✅ Export berhasil! File disimpan: {filename}\")\n", "    print(f\"📁 File berisi 6 sheets:\")\n", "    print(f\"   • Regional_Analysis: Analisis per kabupaten/kota\")\n", "    print(f\"   • Aggregation_Table: Tabel agregasi lengkap\")\n", "    print(f\"   • Summary_Statistics: Statistik deskriptif\")\n", "    print(f\"   • Gender/Age/BMI_Distribution: Distribusi karakteristik\")\n", "    print(f\"   • Model_Results: Performa model ML\")\n", "    print(f\"   • Priority_Ranking: Ranking daerah prioritas\")\n", "\n", "except Exception as e:\n", "    print(f\"❌ Error saat export: {str(e)}\")\n", "    print(f\"💡 Pastikan library openpyxl terinstall: pip install openpyxl\")"], "metadata": {"id": "comprehensive_tables"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## **📊 Langkah 11: Visualisasi Data dan <PERSON>**\n", "\n", "**Tujuan:** Membuat visualisasi yang optimal dan mudah dipahami untuk mendukung analisis stunting\n", "\n", "**<PERSON><PERSON><PERSON> yang dibuat:**\n", "- Distribusi stunting per kabupaten/kota (Top 15)\n", "- <PERSON><PERSON><PERSON><PERSON> karakter<PERSON><PERSON> balita stunting vs non-stunting\n", "- Peta interaktif geografis dengan color coding risiko\n", "- <PERSON><PERSON><PERSON> key variables (Tinggi-Berat untuk validasi data)"], "metadata": {"id": "step11_header"}}, {"cell_type": "code", "source": ["print(\"📊 Membuat Visualisasi Data dan <PERSON>...\")\n", "print(\"=\"*70)\n", "\n", "# Set style untuk visualisasi yang profesional\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "# 1. VISUALISASI DISTRIBUSI STUNTING PER DAERAH\n", "print(\"\\n1️⃣ Visualisasi Distribusi Stunting per Daerah\")\n", "print(\"-\" * 50)\n", "\n", "# Top 15 daerah dengan stunting tertinggi\n", "fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 12))\n", "\n", "# Plot 1: Top 15 stunting tertinggi\n", "top_15_highest = regional_detailed.head(15)\n", "colors_high = ['#ff4444' if x >= 30 else '#ff8800' if x >= 20 else '#44aa44' for x in top_15_highest['Persentase_Stunting']]\n", "\n", "bars1 = ax1.bar(range(len(top_15_highest)), top_15_highest['Persentase_Stunting'], color=colors_high)\n", "ax1.set_title('🔴 Top 15 Kabupaten/Kota dengan Stunting Tertinggi', fontsize=14, fontweight='bold')\n", "ax1.set_ylabel('Persentase Stunting (%)', fontweight='bold')\n", "ax1.set_xticks(range(len(top_15_highest)))\n", "ax1.set_xticklabels([name[:15] + '...' if len(name) > 15 else name for name in top_15_highest.index],\n", "                    rotation=45, ha='right')\n", "ax1.grid(axis='y', alpha=0.3)\n", "\n", "# <PERSON><PERSON>kan nilai di atas bar\n", "for i, bar in enumerate(bars1):\n", "    height = bar.get_height()\n", "    ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5, f'{height:.1f}%',\n", "             ha='center', va='bottom', fontweight='bold')\n", "\n", "# Plot 2: Top 15 stunting terendah\n", "top_15_lowest = regional_detailed.tail(15)\n", "colors_low = ['#44aa44' for _ in top_15_lowest['Persentase_Stunting']]\n", "\n", "bars2 = ax2.bar(range(len(top_15_lowest)), top_15_lowest['Persentase_Stunting'], color=colors_low)\n", "ax2.set_title('🟢 Top 15 Kabupaten/Kota dengan Stunting Terendah', fontsize=14, fontweight='bold')\n", "ax2.set_ylabel('Persentase Stunting (%)', fontweight='bold')\n", "ax2.set_xlabel('Kabupaten/Kota', fontweight='bold')\n", "ax2.set_xticks(range(len(top_15_lowest)))\n", "ax2.set_xticklabels([name[:15] + '...' if len(name) > 15 else name for name in top_15_lowest.index],\n", "                    rotation=45, ha='right')\n", "ax2.grid(axis='y', alpha=0.3)\n", "\n", "# <PERSON><PERSON>kan nilai di atas bar\n", "for i, bar in enumerate(bars2):\n", "    height = bar.get_height()\n", "    ax2.text(bar.get_x() + bar.get_width()/2., height + 0.2, f'{height:.1f}%',\n", "             ha='center', va='bottom', fontweight='bold')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 2. VISUALISASI PERBANDINGAN KARAKTERISTIK\n", "print(\"\\n2️⃣ Visualisasi Perbandingan Karakteristik Balita\")\n", "print(\"-\" * 50)\n", "\n", "fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "fig.suptitle('📊 JABAR STUNTING ANALYZER - Perbandingan Karakteristik Balita: Stunting vs Non-Stunting', fontsize=16, fontweight='bold')\n", "\n", "# Plot 1: Distribusi Tinggi Badan\n", "stunting_data = df_clean[df_clean['status'] == 'Stunting']['tinggi_badan_cm']\n", "non_stunting_data = df_clean[df_clean['status'] == 'Non-Stunting']['tinggi_badan_cm']\n", "\n", "axes[0,0].hist([non_stunting_data, stunting_data], bins=20, alpha=0.7,\n", "               label=['Non-Stunting', 'Stunting'], color=['#44aa44', '#ff4444'])\n", "axes[0,0].set_title('Distribusi Tinggi Badan', fontweight='bold')\n", "axes[0,0].set_xlabel('<PERSON><PERSON><PERSON> (cm)')\n", "axes[0,0].set_ylabel('Fr<PERSON><PERSON><PERSON>')\n", "axes[0,0].legend()\n", "axes[0,0].grid(alpha=0.3)\n", "\n", "# Plot 2: <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "stunting_weight = df_clean[df_clean['status'] == 'Stunting']['berat_badan_kg']\n", "non_stunting_weight = df_clean[df_clean['status'] == 'Non-Stunting']['berat_badan_kg']\n", "\n", "axes[0,1].hist([non_stunting_weight, stunting_weight], bins=20, alpha=0.7,\n", "               label=['Non-Stunting', 'Stunting'], color=['#44aa44', '#ff4444'])\n", "axes[0,1].set_title('Distribusi <PERSON>', fontweight='bold')\n", "axes[0,1].set_xlabel('<PERSON><PERSON> (kg)')\n", "axes[0,1].set_ylabel('<PERSON><PERSON><PERSON><PERSON>')\n", "axes[0,1].legend()\n", "axes[0,1].grid(alpha=0.3)\n", "\n", "# Plot 3: Distribusi BMI\n", "stunting_bmi = df_clean[df_clean['status'] == 'Stunting']['bmi']\n", "non_stunting_bmi = df_clean[df_clean['status'] == 'Non-Stunting']['bmi']\n", "\n", "axes[1,0].hist([non_stunting_bmi, stunting_bmi], bins=20, alpha=0.7,\n", "               label=['Non-Stunting', 'Stunting'], color=['#44aa44', '#ff4444'])\n", "axes[1,0].set_title('Distribusi BMI', fontweight='bold')\n", "axes[1,0].set_xlabel('BMI')\n", "axes[1,0].set_ylabel('Fr<PERSON><PERSON><PERSON>')\n", "axes[1,0].legend()\n", "axes[1,0].grid(alpha=0.3)\n", "\n", "# Plot 4: Distribusi Usia\n", "stunting_age = df_clean[df_clean['status'] == 'Stunting']['usia_balita_bulan']\n", "non_stunting_age = df_clean[df_clean['status'] == 'Non-Stunting']['usia_balita_bulan']\n", "\n", "axes[1,1].hist([non_stunting_age, stunting_age], bins=20, alpha=0.7,\n", "               label=['Non-Stunting', 'Stunting'], color=['#44aa44', '#ff4444'])\n", "axes[1,1].set_title('Distribusi Usia', fontweight='bold')\n", "axes[1,1].set_xlabel('<PERSON><PERSON> (bulan)')\n", "axes[1,1].set_ylabel('<PERSON><PERSON><PERSON><PERSON>')\n", "axes[1,1].legend()\n", "axes[1,1].grid(alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"\\n✅ Visualisasi distribusi dan perbandingan selesai!\")\n", "print(\"📊 Grafik mendukung analisis dan mudah dipahami\")"], "metadata": {"id": "data_visualization"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["### **🗺️ 11.3 Visualisasi Peta Interaktif Stunting Jawa Barat**\n", "\n", "**Tujuan:** Membuat peta interaktif untuk visualisasi geografis distribusi stunting <PERSON> <PERSON><PERSON>\n", "\n", "**<PERSON><PERSON> peta:**\n", "- Choropleth map berdasarkan persentase stunting per kabupaten/kota\n", "- Color coding berdasar<PERSON> kategori risiko (Merah: <PERSON>ggi, Kuning: Sedang, Hijau: Rendah)\n", "- Popup informasi detail untuk setiap daerah\n", "- Legend dan kontrol interaktif"], "metadata": {"id": "map_visualization_header"}}, {"cell_type": "code", "source": ["print(\"🗺️ Membuat Peta Interaktif Stunting Jawa Barat...\")\n", "print(\"=\"*60)\n", "\n", "# 1. PERSIAPAN DATA UNTUK PETA\n", "print(\"\\n1️⃣ Persiapan Data Geografis\")\n", "print(\"-\" * 40)\n", "\n", "# Buat dataset untuk peta dengan koordinat estimasi kabupaten/kota Jawa Barat\n", "# Koordinat ini adalah estimasi untuk keperluan visualisasi\n", "koordinat_jabar = {\n", "    'Ka<PERSON><PERSON> Bandung': [-6.9175, 107.6191],\n", "    'Kab. Bandung Barat': [-6.8186, 107.4917],\n", "    'Ka<PERSON><PERSON>': [-6.2383, 107.0011],\n", "    '<PERSON><PERSON><PERSON>': [-6.5971, 106.8060],\n", "    '<PERSON><PERSON><PERSON>': [-7.3257, 108.3534],\n", "    '<PERSON><PERSON><PERSON>': [-6.8200, 107.1425],\n", "    'Kab. Cirebon': [-6.7063, 108.5571],\n", "    'Ka<PERSON><PERSON>': [-7.2253, 107.8986],\n", "    '<PERSON><PERSON><PERSON>': [-6.3274, 108.3199],\n", "    '<PERSON><PERSON><PERSON>': [-6.3015, 107.3020],\n", "    '<PERSON><PERSON><PERSON>': [-6.9759, 108.4836],\n", "    '<PERSON><PERSON><PERSON>': [-6.8360, 108.2277],\n", "    'Ka<PERSON><PERSON> Pangandaran': [-7.6867, 108.6500],\n", "    '<PERSON><PERSON><PERSON><PERSON><PERSON>': [-6.5569, 107.4431],\n", "    'Ka<PERSON><PERSON>': [-6.5627, 107.7539],\n", "    'Ka<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>': [-6.9278, 106.9571],\n", "    'Kab. <PERSON>': [-6.8387, 107.9214],\n", "    'Kab. Tasikmalaya': [-7.3506, 108.2172],\n", "    'Kota Bandung': [-6.9175, 107.6191],\n", "    'Kota Banjar': [-7.3729, 108.5389],\n", "    'Kota Bekasi': [-6.2383, 107.0011],\n", "    'Kota Bogor': [-6.5971, 106.8060],\n", "    'Kota Cimahi': [-6.8723, 107.5425],\n", "    'Kota Cirebon': [-6.7320, 108.5520],\n", "    'Kota Depok': [-6.4025, 106.7942],\n", "    'Kota Sukabumi': [-6.9278, 106.9571],\n", "    'Kota Tasikmalaya': [-7.3274, 108.2207]\n", "}\n", "\n", "# Gabungkan data stunting dengan koordinat\n", "map_data = []\n", "for region in regional_detailed.index:\n", "    if region in koordinat_jabar:\n", "        lat, lon = koordinat_jabar[region]\n", "        stunting_pct = regional_detailed.loc[region, 'Persentase_Stunting']\n", "        total_balita = regional_detailed.loc[region, 'Total_Balita']\n", "        stunting_count = regional_detailed.loc[region, 'Jumlah_Stunting']\n", "        kategori_risiko = regional_detailed.loc[region, 'Kategori_Risiko']\n", "        rata_tinggi = regional_detailed.loc[region, 'Rata_Tinggi']\n", "        rata_bmi = regional_detailed.loc[region, 'Rata_BMI']\n", "\n", "        map_data.append({\n", "            'region': region,\n", "            'latitude': lat,\n", "            'longitude': lon,\n", "            'stunting_pct': stunting_pct,\n", "            'total_balita': total_balita,\n", "            'stunting_count': stunting_count,\n", "            'kategori_risiko': kate<PERSON>i_risiko,\n", "            'rata_tinggi': rata_tinggi,\n", "            'rata_bmi': rata_bmi\n", "        })\n", "\n", "map_df = pd.DataFrame(map_data)\n", "print(f\"✅ Data geografis siap: {len(map_df)} daerah dengan koordinat\")\n", "\n", "# 2. MEMBUAT PETA INTERAKTIF\n", "print(\"\\n2️⃣ Membuat Peta Interaktif\")\n", "print(\"-\" * 40)\n", "\n", "# Inisialisasi peta dengan center di Jawa Barat\n", "center_lat = -6.9175\n", "center_lon = 107.6191\n", "m = folium.Map(\n", "    location=[center_lat, center_lon],\n", "    zoom_start=9,\n", "    tiles='OpenStreetMap'\n", ")\n", "\n", "# Fungsi untuk menentukan warna berdasarkan kategori risiko\n", "def get_color(kate<PERSON><PERSON>_risi<PERSON>):\n", "    if kate<PERSON>i_risiko == 'Tinggi':\n", "        return 'red'\n", "    elif <PERSON>_risiko == 'Sedang':\n", "        return 'orange'\n", "    else:\n", "        return 'green'\n", "\n", "# Fungsi untuk menentukan ukuran marker berda<PERSON><PERSON> persentase stunting\n", "def get_radius(stunting_pct):\n", "    return max(5, min(25, stunting_pct * 0.8))  # <PERSON><PERSON> 5-25\n", "\n", "# Tambahkan marker untuk setiap daerah\n", "for _, row in map_df.iterrows():\n", "    # Buat popup dengan informasi detail\n", "    popup_html = f\"\"\"\n", "    <div style=\"font-family: Arial; width: 250px;\">\n", "        <h4 style=\"color: {get_color(row['kate<PERSON><PERSON>_risiko'])}; margin-bottom: 10px;\">\n", "            📍 {row['region']}\n", "        </h4>\n", "        <hr style=\"margin: 5px 0;\">\n", "        <p><b>🎯 Stunting:</b> {row['stunting_pct']:.1f}% ({int(row['stunting_count'])}/{int(row['total_balita'])} balita)</p>\n", "        <p><b>⚠️ Kategori Risiko:</b> <span style=\"color: {get_color(row['kategori_risiko'])};\"><b>{row['kategori_risiko']}</b></span></p>\n", "        <p><b>📏 Rata-rata Tinggi:</b> {row['rata_tinggi']:.1f} cm</p>\n", "        <p><b>⚖️ Rata-rata BMI:</b> {row['rata_bmi']:.1f}</p>\n", "    </div>\n", "    \"\"\"\n", "\n", "    # Tambahkan circle marker\n", "    folium.CircleMarker(\n", "        location=[row['latitude'], row['longitude']],\n", "        radius=get_radius(row['stunting_pct']),\n", "        popup=folium.Popup(popup_html, max_width=300),\n", "        color='black',\n", "        weight=2,\n", "        fillColor=get_color(row['kate<PERSON><PERSON>_risiko']),\n", "        fillOpacity=0.7,\n", "        tooltip=f\"{row['region']}: {row['stunting_pct']:.1f}% stunting\"\n", "    ).add_to(m)\n", "\n", "# Tambahkan legend\n", "legend_html = '''\n", "<div style=\"position: fixed;\n", "            bottom: 50px; left: 50px; width: 200px; height: 120px;\n", "            background-color: white; border:2px solid grey; z-index:9999;\n", "            font-size:14px; padding: 10px\">\n", "<h4>🗺️ Legend Stunting</h4>\n", "<p><i class=\"fa fa-circle\" style=\"color:red\"></i> <PERSON><PERSON><PERSON> (≥30%)</p>\n", "<p><i class=\"fa fa-circle\" style=\"color:orange\"></i> <PERSON><PERSON><PERSON> (20-29%)</p>\n", "<p><i class=\"fa fa-circle\" style=\"color:green\"></i> <PERSON><PERSON><PERSON> (<20%)</p>\n", "<p><small>Ukuran lingkaran = % stunting</small></p>\n", "</div>\n", "'''\n", "m.get_root().html.add_child(folium.Element(legend_html))\n", "\n", "# Tambahkan title\n", "title_html = '''\n", "<h3 align=\"center\" style=\"font-size:20px\"><b>🗺️ JABAR STUNTING ANALYZER - Peta Interaktif Stunting Balita Jawa Barat</b></h3>\n", "'''\n", "m.get_root().html.add_child(folium.Element(title_html))\n", "\n", "print(\"✅ Peta interaktif berhasil dibuat!\")\n", "print(f\"📊 {len(map_df)} da<PERSON>h ditampilkan dengan color coding risiko\")\n", "print(\"🖱️ Klik marker untuk melihat detail informasi\")\n", "\n", "# <PERSON><PERSON><PERSON><PERSON> peta\n", "display(m)\n", "\n", "# 3. STATISTIK PETA\n", "print(\"\\n3️⃣ Statistik Distribusi Geografis\")\n", "print(\"-\" * 40)\n", "\n", "risk_distribution = map_df['kate<PERSON><PERSON>_risi<PERSON>'].value_counts()\n", "print(\"📊 Distribusi Risiko di Peta:\")\n", "for risk, count in risk_distribution.items():\n", "    pct = (count / len(map_df) * 100)\n", "    icon = '🔴' if risk == 'Tinggi' else '🟡' if risk == 'Sedang' else '🟢'\n", "    print(f\"   {icon} {risk}: {count} daerah ({pct:.1f}%)\")\n", "\n", "print(f\"\\n📍 Hotspot Stunting (Top 5):\")\n", "top_5_map = map_df.nlargest(5, 'stunting_pct')\n", "for i, (_, row) in enumerate(top_5_map.iterrows(), 1):\n", "    print(f\"   {i}. {row['region']}: {row['stunting_pct']:.1f}%\")\n", "\n", "print(\"\\n✅ Visualisasi peta interaktif selesai!\")\n", "print(\"🗺️ Peta memberikan perspektif geografis yang valuable untuk stakeholder\")"], "metadata": {"id": "interactive_map"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["### **📈 11.4 <PERSON><PERSON><PERSON> Variables**\n", "\n", "**Tujuan:** Menganalisis korelasi antar variabel kunci untuk validasi data dan insights\n", "\n", "**<PERSON><PERSON><PERSON> yang <PERSON>:**\n", "- <PERSON><PERSON><PERSON> vs <PERSON><PERSON> (validasi data antropometri)\n", "- Analisis BMI berdasarkan status stunting\n", "- Summary insights untuk decision making"], "metadata": {"id": "correlation_analysis_header"}}, {"cell_type": "code", "source": ["print(\"📈 <PERSON><PERSON><PERSON> Variables...\")\n", "print(\"=\"*50)\n", "\n", "# 1. VALIDASI DATA: KORELASI TINGGI VS BERAT\n", "print(\"\\n1️⃣ Validasi Data: <PERSON><PERSON><PERSON> vs <PERSON><PERSON>\")\n", "print(\"-\" * 50)\n", "\n", "# Hitung korelasi untuk validasi data\n", "correlation_height_weight = df_clean['tinggi_badan_cm'].corr(df_clean['berat_badan_kg'])\n", "print(f\"📊 <PERSON><PERSON><PERSON>: {correlation_height_weight:.4f}\")\n", "\n", "# Interpretasi k<PERSON>i\n", "if correlation_height_weight > 0.7:\n", "    print(f\"   ✅ Korelasi KUAT - Data antropometri valid dan konsisten\")\n", "elif correlation_height_weight > 0.5:\n", "    print(f\"   ⚠️ Korelasi SEDANG - Data cukup valid dengan beberapa variasi\")\n", "else:\n", "    print(f\"   ❌ Korelasi LEMAH - Perlu review kualitas data\")\n", "\n", "# Buat scatter plot sederhana untuk validasi visual\n", "plt.figure(figsize=(10, 6))\n", "colors = {'Stunting': '#ff4444', 'Non-Stunting': '#44aa44'}\n", "for status in df_clean['status'].unique():\n", "    subset = df_clean[df_clean['status'] == status]\n", "    plt.scatter(subset['tinggi_badan_cm'], subset['berat_badan_kg'],\n", "               c=colors[status], label=status, alpha=0.6, s=30)\n", "\n", "plt.xlabel('<PERSON><PERSON><PERSON> (cm)', fontsize=12)\n", "plt.ylabel('<PERSON><PERSON> (kg)', fontsize=12)\n", "plt.title(f'📊 Validasi Data: <PERSON><PERSON><PERSON> vs <PERSON><PERSON>\\n(Korelasi: {correlation_height_weight:.3f})', fontsize=14, fontweight='bold')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 2. ANALISIS BMI BERDASARKAN STATUS STUNTING\n", "print(\"\\n2️⃣ Analisis BMI berdasarkan Status Stunting\")\n", "print(\"-\" * 50)\n", "\n", "# Hitung statistik BMI\n", "bmi_stunting = df_clean[df_clean['status'] == 'Stunting']['bmi'].mean()\n", "bmi_non_stunting = df_clean[df_clean['status'] == 'Non-Stunting']['bmi'].mean()\n", "bmi_diff = abs(bmi_non_stunting - bmi_stunting)\n", "\n", "print(f\"📊 Rata-rata BMI Stunting: {bmi_stunting:.2f}\")\n", "print(f\"📊 Rata-rata BMI Non-Stunting: {bmi_non_stunting:.2f}\")\n", "print(f\"📊 Selisih BMI: {bmi_diff:.2f}\")\n", "\n", "# Interpretasi BMI\n", "if bmi_diff > 2:\n", "    print(f\"   ✅ Perbedaan BMI SIGNIFIKAN - BMI dapat menjadi indikator stunting\")\n", "elif bmi_diff > 1:\n", "    print(f\"   ⚠️ Perbedaan BMI SEDANG - BMI cukup informatif\")\n", "else:\n", "    print(f\"   ❌ Perbedaan BMI KECIL - BMI kurang diskriminatif\")\n", "\n", "# 3. SUMMARY INSIGHTS UNTUK DECISION MAKING\n", "print(\"\\n3️⃣ Summary Insights untuk Decision Making\")\n", "print(\"-\" * 50)\n", "\n", "print(\"💡 KEY INSIGHTS:\")\n", "print(f\"   • Validitas Data: {'Baik' if correlation_height_weight > 0.6 else 'Perlu Review'}\")\n", "print(f\"   • BMI sebagai Indikator: {'Efektif' if bmi_diff > 1.5 else 'Kurang Efektif'}\")\n", "print(f\"   • Konsistensi Antropometri: {'Tinggi' if correlation_height_weight > 0.7 else 'Sedang'}\")\n", "\n", "print(\"\\n🎯 REKOMENDASI ANALISIS:\")\n", "if correlation_height_weight > 0.7 and bmi_diff > 1.5:\n", "    print(\"   ✅ Data berkualitas tinggi, analisis ML dapat diandalkan\")\n", "    print(\"   ✅ BMI dan tinggi-berat dapat digunakan sebagai fitur utama\")\n", "elif correlation_height_weight > 0.5:\n", "    print(\"   ⚠️ Data cukup baik, perlu validasi tambahan\")\n", "    print(\"   ⚠️ Gunakan multiple features untuk akurasi lebih baik\")\n", "else:\n", "    print(\"   ❌ Perlu review dan cleaning data lebih lanjut\")\n", "    print(\"   ❌ Pertimbangkan pengumpulan data tambahan\")\n", "\n", "print(\"\\n✅ <PERSON><PERSON><PERSON> Variables selesai!\")\n", "print(\"📊 Insights siap untuk mendukung decision making\")"], "metadata": {"id": "correlation_analysis"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## **🎯 Langkah 12: <PERSON><PERSON><PERSON><PERSON><PERSON> - Dashboard Tabel dan <PERSON>**\n", "\n", "**Tujuan:** Memberikan kesimpulan komprehensif hasil analisis sistem JABAR STUNTING ANALYZER dalam format tabel dan visualisasi yang mudah dipahami\n", "\n", "**Output yang dihasilkan:**\n", "- 📊 Executive Summary Dashboard Table\n", "- 📈 Model Performance Comparison Chart\n", "- 🗺️ Regional Risk Assessment Matrix\n", "- 📋 Strategic Action Plan Table\n", "- 🚀 Impact Projection Visualization\n", "\n", "**Keunggulan Format Baru:**\n", "- Visual dan mudah dipahami semua pengguna\n", "- Informasi terstruktur dalam tabel yang rapi\n", "- Grafik interaktif untuk decision making\n", "- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>"], "metadata": {"id": "step12_header"}}, {"cell_type": "code", "source": ["print(\"🎯 Kesimpulan dan Rekomendasi Strategis JABAR STUNTING ANALYZER\")\n", "print(\"=\"*80)\n", "\n", "# 1. SUMMARY HASIL MACHINE LEARNING\n", "print(\"\\n1️⃣ SUMMARY HASIL MACHINE LEARNING\")\n", "print(\"-\" * 50)\n", "\n", "print(f\"🤖 MODEL TERBAIK: {best_model_name}\")\n", "print(f\"   📊 Akurasi: {models_results[best_model_name]['accuracy']*100:.2f}%\")\n", "print(f\"   📊 F1-Score: {models_results[best_model_name]['f1_score']:.4f}\")\n", "print(f\"   📊 Precision: {models_results[best_model_name]['precision']:.4f}\")\n", "print(f\"   📊 Recall: {models_results[best_model_name]['recall']:.4f}\")\n", "print(f\"   ⚡ Waktu Training: {models_results[best_model_name]['training_time']:.4f} detik\")\n", "\n", "print(f\"\\n💡 INTERPRETASI MODEL:\")\n", "print(f\"   • Model dapat memprediksi stunting dengan a<PERSON> {models_results[best_model_name]['accuracy']*100:.1f}%\")\n", "print(f\"   • <PERSON>g<PERSON> kes<PERSON>han prediksi: {(1-models_results[best_model_name]['accuracy'])*100:.1f}%\")\n", "print(f\"   • Model cocok untuk screening awal dan identifikasi risiko\")\n", "print(f\"   • Performa balanced antara precision dan recall\")\n", "\n", "# 2. INSIGHTS UTAMA ANALISIS REGIONAL\n", "print(\"\\n2️⃣ INSIGHTS UTAMA ANALISIS REGIONAL\")\n", "print(\"-\" * 50)\n", "\n", "avg_stunting = regional_detailed['Persentase_Stunting'].mean()\n", "highest_region = regional_detailed.index[0]\n", "lowest_region = regional_detailed.index[-1]\n", "highest_pct = regional_detailed['Persentase_Stunting'].iloc[0]\n", "lowest_pct = regional_detailed['Persentase_Stunting'].iloc[-1]\n", "\n", "print(f\"📊 STATISTIK KUNCI:\")\n", "print(f\"   • Rata-rata stunting <PERSON><PERSON>: {avg_stunting:.2f}%\")\n", "print(f\"   • Total kabupaten/kota dianalisis: {len(regional_detailed)}\")\n", "print(f\"   • <PERSON><PERSON><PERSON> stunting tertinggi: {highest_region} ({highest_pct:.2f}%)\")\n", "print(f\"   • <PERSON><PERSON><PERSON> stunting terendah: {lowest_region} ({lowest_pct:.2f}%)\")\n", "print(f\"   • Rentang stunting: {highest_pct - lowest_pct:.2f} poin persentase\")\n", "\n", "# Hitung distribusi risiko\n", "risk_counts = regional_detailed['Kate<PERSON>i_Risiko'].value_counts()\n", "print(f\"\\n🎯 DISTRIBUSI RISIKO:\")\n", "for risk in ['Tinggi', 'Sedang', 'Rendah']:\n", "    if risk in risk_counts.index:\n", "        count = risk_counts[risk]\n", "        pct = (count / len(regional_detailed) * 100)\n", "        icon = '🔴' if risk == 'Tinggi' else '🟡' if risk == 'Sedang' else '🟢'\n", "        print(f\"   {icon} R<PERSON><PERSON> {risk}: {count} daerah ({pct:.1f}%)\")\n", "\n", "# 3. R<PERSON>KOMENDASI INTERVENSI BERDASARKAN KATEGORI RISIKO\n", "print(\"\\n3️⃣ REKOMENDASI INTERVENSI STRATEGIS\")\n", "print(\"-\" * 50)\n", "\n", "print(\"🔴 DAERAH RISIKO TINGGI (≥30% stunting):\")\n", "high_risk_regions = regional_detailed[regional_detailed['Kategori_Risiko'] == 'Tinggi']\n", "if len(high_risk_regions) > 0:\n", "    print(f\"   📍 Jumlah: {len(high_risk_regions)} daerah\")\n", "    print(f\"   🎯 Prioritas: INTERVENSI DARURAT\")\n", "    print(f\"   💡 Rekomendasi:\")\n", "    print(f\"   • Program gizi darurat dan suplementasi\")\n", "    print(f\"   • Screening kesehatan massal balita\")\n", "    print(f\"   • <PERSON><PERSON><PERSON> intensif ibu dan keluarga\")\n", "    print(f\"   • Monitoring ketat pertumbuhan balita\")\n", "    print(f\"   • <PERSON><PERSON><PERSON><PERSON> lintas sektor (kesehatan, sosial, ekonomi)\")\n", "else:\n", "    print(f\"   ✅ Tidak ada daerah dengan risiko tinggi\")\n", "\n", "print(f\"\\n🟡 DAERAH RISIKO SEDANG (20-29% stunting):\")\n", "medium_risk_regions = regional_detailed[regional_detailed['Kategori_Risiko'] == 'Sedang']\n", "if len(medium_risk_regions) > 0:\n", "    print(f\"   📍 Jumlah: {len(medium_risk_regions)} daerah\")\n", "    print(f\"   🎯 Prioritas: INTERVENSI PREVENTIF\")\n", "    print(f\"   💡 Rekomendasi:\")\n", "    print(f\"   • Program pencegahan stunting terintegrasi\")\n", "    print(f\"   • Peningkatan akses pelayanan kesehatan\")\n", "    print(f\"   • <PERSON><PERSON><PERSON> gizi dan pola asuh\")\n", "    print(f\"   • Monitoring rutin pertumbuhan balita\")\n", "    print(f\"   • Pemberdayaan masyarakat\")\n", "else:\n", "    print(f\"   ✅ Tidak ada daerah dengan risiko sedang\")\n", "\n", "print(f\"\\n🟢 DAERAH RISIKO RENDAH (<20% stunting):\")\n", "low_risk_regions = regional_detailed[regional_detailed['Kategori_Risiko'] == 'Rendah']\n", "if len(low_risk_regions) > 0:\n", "    print(f\"   📍 Jumlah: {len(low_risk_regions)} daerah\")\n", "    print(f\"   🎯 Prioritas: MAINTENANCE & BEST PRACTICE\")\n", "    print(f\"   💡 Rekomendasi:\")\n", "    print(f\"   • Pertahankan program yang sudah berjalan baik\")\n", "    print(f\"   • Dokumentasi best practice untuk replikasi\")\n", "    print(f\"   • Monitoring berkala untuk early warning\")\n", "    print(f\"   • Sharing knowledge ke daerah lain\")\n", "    print(f\"   • Inovasi program pencegahan\")\n", "else:\n", "    print(f\"   ⚠️ Semua daerah memerlukan intervensi\")\n", "\n", "# 4. ACTION PLAN UNTUK STAKEHOLDER\n", "print(\"\\n4️⃣ ACTION PLAN UNTUK STAKEHOLDER\")\n", "print(\"-\" * 50)\n", "\n", "print(\"🏛️ PEMERINTAH PROVINSI JAWA BARAT:\")\n", "print(\"   • Alokasi anggaran prioritas untuk daerah risiko tinggi\")\n", "print(\"   • Koordinasi lintas dinas untuk program terintegrasi\")\n", "print(\"   • Monitoring dan evaluasi berkala\")\n", "print(\"   • Capacity building untuk tenaga kesehatan\")\n", "\n", "print(\"\\n🏥 DINAS KESEHATAN:\")\n", "print(\"   • Implementasi screening rutin menggunakan model ML\")\n", "print(\"   • <PERSON><PERSON><PERSON><PERSON> tenaga kesehatan untuk early detection\")\n", "print(\"   • Sistem rujukan yang efektif\")\n", "print(\"   • Database terintegrasi untuk monitoring\")\n", "\n", "print(\"\\n👨‍👩‍👧‍👦 MASYARAKAT DAN KELUARGA:\")\n", "print(\"   • <PERSON><PERSON><PERSON> gizi dan pola asuh yang benar\")\n", "print(\"   • Peman<PERSON>uan pertumbuhan balita secara rutin\")\n", "print(\"   • Partisipasi aktif dalam program posyandu\")\n", "print(\"   • Pelaporan dini jika ada tanda-tanda stunting\")\n", "\n", "print(\"\\n🎓 AKADEMISI DAN PENELITI:\")\n", "print(\"   • Penelitian lanjutan untuk faktor risiko lokal\")\n", "print(\"   • Pengembangan model prediksi yang lebih akurat\")\n", "print(\"   • Evaluasi efektivitas intervensi\")\n", "print(\"   • Publikasi hasil untuk knowledge sharing\")\n", "\n", "# 5. KESIMPULAN AKHIR\n", "print(\"\\n\" + \"=\"*80)\n", "print(\"🎯 KESIMPULAN AKHIR JABAR STUNTING ANALYZER\")\n", "print(\"=\"*80)\n", "\n", "print(f\"✅ PENCAPAIAN SISTEM:\")\n", "print(f\"   • <PERSON><PERSON><PERSON><PERSON> menganalisis {len(df_clean)} data balita dari {len(regional_detailed)} daerah\")\n", "print(f\"   • Model ML {best_model_name} dengan a<PERSON> {models_results[best_model_name]['accuracy']*100:.1f}%\")\n", "print(f\"   • Peta interaktif geografis dengan visualisasi risiko stunting\")\n", "print(f\"   • <PERSON><PERSON><PERSON> key variables untuk validasi data\")\n", "print(f\"   • Identifikasi daerah prioritas intervensi\")\n", "print(f\"   • Rekomendasi strategis berbasis data\")\n", "\n", "print(f\"\\n🚀 DAMPAK YANG DIHARAPKAN:\")\n", "print(f\"   • Penurunan prevalensi stunting di Jawa Barat\")\n", "print(f\"   • <PERSON>vensi yang lebih tepat sasaran dan efisien\")\n", "print(f\"   • Visualisasi interaktif untuk decision making yang lebih baik\")\n", "print(f\"   • Sistem monitoring yang berbasis data dan geografis\")\n", "print(f\"   • Peningkatan kualitas hidup balita Jawa Barat\")\n", "\n", "print(f\"\\n📊 SISTEM JABAR STUNTING ANALYZER SIAP DIGUNAKAN!\")\n", "print(f\"🎯 Terima kasih telah menggunakan sistem analisis stunting Jawa <PERSON>\")\n", "print(f\"👥 Dikembangkan oleh Kelompok 9: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>\")\n", "\n", "print(\"\\n\" + \"=\"*80)"], "metadata": {"id": "conclusions_recommendations"}, "execution_count": null, "outputs": []}]}