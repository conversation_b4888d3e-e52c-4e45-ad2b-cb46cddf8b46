# Install library tambahan yang diperlukan untuk analisis
!pip install tabulate seaborn folium plotly openpyxl -q

# Import library untuk manipulasi dan analisis data
import pandas as pd
import numpy as np

# Import library untuk visualisasi data
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# Import library untuk visualisasi peta interaktif
import folium
from folium import plugins

# Import library untuk machine learning
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.tree import DecisionTreeClassifier
from sklearn.neighbors import KNeighborsClassifier
from sklearn.naive_bayes import GaussianNB
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    classification_report, confusion_matrix
)

# Import library untuk format tabel profesional
from tabulate import tabulate

# Import library untuk pengukuran waktu eksekusi
import time
import warnings
warnings.filterwarnings('ignore')  # Menyembunyikan warning yang tidak perlu

# Konfigurasi tampilan untuk visualisasi yang optimal
plt.style.use('default')
sns.set_palette("husl")
pd.set_option('display.max_columns', None)
pd.set_option('display.width', None)

print("✅ Semua library berhasil diimport dan dikonfigurasi!")
print("🚀 Environment siap untuk analisis stunting Jawa Barat")
print("📊 Sistem JABAR STUNTING ANALYZER siap dijalankan")
print("🗺️ Library peta interaktif (folium) dan scatter plot (plotly) siap!")

# Memuat dataset stunting balita Jawa Barat
print("📂 Memuat dataset stunting balita Jawa Barat...")
df = pd.read_csv('Balita_Stunting_Provinsi_Jawa_Barat.csv')

print("✅ Dataset berhasil dimuat!")
print(f"📊 Ukuran dataset: {df.shape[0]} baris, {df.shape[1]} kolom")
print("\n" + "="*60)
print("📋 INFORMASI DASAR DATASET")
print("="*60)

# Tampilkan 5 baris pertama dataset
print("\n📝 5 Baris Pertama Dataset:")
print(df.head())

# Informasi detail tentang dataset
print("\n📊 Informasi Kolom:")
print(df.info())

# Statistik deskriptif untuk kolom numerik
print("\n📈 Statistik Deskriptif:")
print(df.describe())

# Nilai unik per kolom
print("\n🔍 Nilai Unik per Kolom:")
for col in df.columns:
    unique_count = df[col].nunique()
    print(f"  {col}: {unique_count} nilai unik")

# Cek missing values
print("\n⚠️ Missing Values:")
missing_data = df.isnull().sum()
missing_percent = (missing_data / len(df)) * 100
missing_df = pd.DataFrame({
    'Kolom': missing_data.index,
    'Jumlah Missing': missing_data.values,
    'Persentase (%)': missing_percent.values
})
print(missing_df[missing_df['Jumlah Missing'] > 0])

print("\n✅ Eksplorasi dataset awal selesai!")

print("🔧 Memulai Data Preprocessing dan Pembersihan...")
print("="*50)

# Buat copy dataset untuk preprocessing
df_clean = df.copy()

# 1. Tangani missing values dengan metode yang tepat
print("\n1️⃣ Menangani Missing Values:")
print(f"   Missing values sebelum: {df_clean.isnull().sum().sum()}")

# Isi missing values dengan median untuk kolom numerik (lebih robust terhadap outlier)
numeric_cols = ['tinggi_badan_cm', 'berat_badan_kg']
for col in numeric_cols:
    if df_clean[col].isnull().sum() > 0:
        median_val = df_clean[col].median()
        df_clean[col].fillna(median_val, inplace=True)
        print(f"   ✅ {col}: diisi dengan median ({median_val})")

print(f"   Missing values setelah: {df_clean.isnull().sum().sum()}")

# 2. Standardisasi jenis kelamin ke format yang konsisten
print("\n2️⃣ Standardisasi Jenis Kelamin:")
print(f"   Format sebelum: {df_clean['jenis_kelamin'].unique()}")

# Mapping jenis kelamin ke format standar
gender_mapping = {
    'L': 'Laki-laki',
    'P': 'Perempuan',
    'Laki-laki': 'Laki-laki',
    'Perempuan': 'Perempuan'
}
df_clean['jenis_kelamin'] = df_clean['jenis_kelamin'].map(gender_mapping)
print(f"   Format setelah: {df_clean['jenis_kelamin'].unique()}")

# 3. Buat fitur tambahan untuk analisis yang lebih mendalam
print("\n3️⃣ Membuat Fitur Tambahan:")

# BMI (Body Mass Index) - indikator penting untuk status gizi
df_clean['tinggi_m'] = df_clean['tinggi_badan_cm'] / 100
df_clean['bmi'] = df_clean['berat_badan_kg'] / (df_clean['tinggi_m'] ** 2)
print(f"   ✅ BMI: rata-rata {df_clean['bmi'].mean():.2f}")

# Kategori usia untuk analisis yang lebih spesifik
def kategorikan_usia(usia):
    if usia <= 12:
        return '0-12 bulan'
    elif usia <= 24:
        return '13-24 bulan'
    elif usia <= 36:
        return '25-36 bulan'
    elif usia <= 48:
        return '37-48 bulan'
    else:
        return '49-60 bulan'

df_clean['kategori_usia'] = df_clean['usia_balita_bulan'].apply(kategorikan_usia)
print(f"   ✅ Kategori usia: {df_clean['kategori_usia'].nunique()} kategori")

# Kategori BMI berdasarkan standar WHO untuk balita
def kategorikan_bmi(bmi):
    if bmi < 16:
        return 'Sangat Kurus'
    elif bmi < 18.5:
        return 'Kurus'
    elif bmi < 25:
        return 'Normal'
    elif bmi < 30:
        return 'Gemuk'
    else:
        return 'Obesitas'

df_clean['kategori_bmi'] = df_clean['bmi'].apply(kategorikan_bmi)
print(f"   ✅ Kategori BMI: {df_clean['kategori_bmi'].nunique()} kategori")

print("\n✅ Data preprocessing selesai!")
print(f"📊 Dataset final: {df_clean.shape[0]} baris, {df_clean.shape[1]} kolom")
print(f"📋 Kolom baru: tinggi_m, bmi, kategori_usia, kategori_bmi")

print("📊 Memulai Exploratory Data Analysis (EDA)...")
print("="*60)

# 1. Analisis distribusi status stunting secara keseluruhan
print("\n1️⃣ DISTRIBUSI STATUS STUNTING JAWA BARAT")
print("-" * 40)
status_counts = df_clean['status'].value_counts()
status_percent = df_clean['status'].value_counts(normalize=True) * 100

for status in status_counts.index:
    count = status_counts[status]
    percent = status_percent[status]
    print(f"   {status}: {count:,} balita ({percent:.2f}%)")

# 2. Analisis stunting per kabupaten/kota
print("\n2️⃣ ANALISIS PER KABUPATEN/KOTA")
print("-" * 40)
print(f"   Total kabupaten/kota: {df_clean['nama_kabupaten_kota'].nunique()}")

# Hitung tingkat stunting per daerah
regional_analysis = df_clean.groupby('nama_kabupaten_kota').agg({
    'status': ['count', lambda x: (x == 'Stunting').sum()]
}).round(2)

regional_analysis.columns = ['Total_Balita', 'Jumlah_Stunting']
regional_analysis['Persentase_Stunting'] = (regional_analysis['Jumlah_Stunting'] / regional_analysis['Total_Balita'] * 100).round(2)
regional_analysis = regional_analysis.sort_values('Persentase_Stunting', ascending=False)

print(f"   Rata-rata stunting Jawa Barat: {regional_analysis['Persentase_Stunting'].mean():.2f}%")
print(f"   Daerah dengan stunting tertinggi: {regional_analysis.index[0]} ({regional_analysis['Persentase_Stunting'].iloc[0]:.2f}%)")
print(f"   Daerah dengan stunting terendah: {regional_analysis.index[-1]} ({regional_analysis['Persentase_Stunting'].iloc[-1]:.2f}%)")

# 3. Analisis berdasarkan karakteristik balita
print("\n3️⃣ ANALISIS BERDASARKAN KARAKTERISTIK BALITA")
print("-" * 50)

# Berdasarkan jenis kelamin
gender_stunting = df_clean.groupby(['jenis_kelamin', 'status']).size().unstack(fill_value=0)
gender_stunting['Total'] = gender_stunting.sum(axis=1)
gender_stunting['Persentase_Stunting'] = (gender_stunting['Stunting'] / gender_stunting['Total'] * 100).round(2)

print("   👶 Berdasarkan Jenis Kelamin:")
for gender in gender_stunting.index:
    stunting_count = gender_stunting.loc[gender, 'Stunting']
    total_count = gender_stunting.loc[gender, 'Total']
    percentage = gender_stunting.loc[gender, 'Persentase_Stunting']
    print(f"   {gender}: {stunting_count}/{total_count} balita ({percentage}%)")

# Berdasarkan kategori usia
age_stunting = df_clean.groupby(['kategori_usia', 'status']).size().unstack(fill_value=0)
age_stunting['Total'] = age_stunting.sum(axis=1)
age_stunting['Persentase_Stunting'] = (age_stunting['Stunting'] / age_stunting['Total'] * 100).round(2)

print("\n   📅 Berdasarkan Kategori Usia:")
for age_cat in age_stunting.index:
    stunting_count = age_stunting.loc[age_cat, 'Stunting']
    total_count = age_stunting.loc[age_cat, 'Total']
    percentage = age_stunting.loc[age_cat, 'Persentase_Stunting']
    print(f"   {age_cat}: {stunting_count}/{total_count} balita ({percentage}%)")

# Berdasarkan kategori BMI
bmi_stunting = df_clean.groupby(['kategori_bmi', 'status']).size().unstack(fill_value=0)
bmi_stunting['Total'] = bmi_stunting.sum(axis=1)
bmi_stunting['Persentase_Stunting'] = (bmi_stunting['Stunting'] / bmi_stunting['Total'] * 100).round(2)

print("\n   ⚖️ Berdasarkan Kategori BMI:")
for bmi_cat in bmi_stunting.index:
    stunting_count = bmi_stunting.loc[bmi_cat, 'Stunting']
    total_count = bmi_stunting.loc[bmi_cat, 'Total']
    percentage = bmi_stunting.loc[bmi_cat, 'Persentase_Stunting']
    print(f"   {bmi_cat}: {stunting_count}/{total_count} balita ({percentage}%)")

print("\n✅ Exploratory Data Analysis selesai!")
print("📊 Insights awal telah diperoleh untuk analisis lanjutan")

print("🤖 Mempersiapkan Data untuk Machine Learning...")
print("="*50)

# Buat copy dataset untuk machine learning
df_ml = df_clean.copy()

# 1. Encoding variabel kategorikal menjadi numerik
print("\n1️⃣ Encoding Variabel Kategorikal:")

# Label Encoder untuk variabel kategorikal
le_gender = LabelEncoder()
le_region = LabelEncoder()
le_age_cat = LabelEncoder()
le_bmi_cat = LabelEncoder()
le_status = LabelEncoder()

# Encoding semua variabel kategorikal
df_ml['jenis_kelamin_encoded'] = le_gender.fit_transform(df_ml['jenis_kelamin'])
df_ml['kabupaten_encoded'] = le_region.fit_transform(df_ml['nama_kabupaten_kota'])
df_ml['kategori_usia_encoded'] = le_age_cat.fit_transform(df_ml['kategori_usia'])
df_ml['kategori_bmi_encoded'] = le_bmi_cat.fit_transform(df_ml['kategori_bmi'])
df_ml['status_encoded'] = le_status.fit_transform(df_ml['status'])

print(f"   ✅ Jenis kelamin: {df_ml['jenis_kelamin'].nunique()} kategori → encoded")
print(f"   ✅ Kabupaten/kota: {df_ml['nama_kabupaten_kota'].nunique()} kategori → encoded")
print(f"   ✅ Kategori usia: {df_ml['kategori_usia'].nunique()} kategori → encoded")
print(f"   ✅ Kategori BMI: {df_ml['kategori_bmi'].nunique()} kategori → encoded")
print(f"   ✅ Status: {df_ml['status'].nunique()} kategori → encoded")

# 2. Pemilihan fitur yang relevan untuk model
print("\n2️⃣ Pemilihan Fitur untuk Model:")
feature_columns = [
    'tahun', 'tinggi_badan_cm', 'berat_badan_kg', 'usia_balita_bulan', 'bmi',
    'jenis_kelamin_encoded', 'kabupaten_encoded', 'kategori_usia_encoded', 'kategori_bmi_encoded'
]

X = df_ml[feature_columns]
y = df_ml['status_encoded']

print(f"   ✅ Jumlah fitur: {len(feature_columns)}")
print(f"   ✅ Fitur yang digunakan: {feature_columns}")
print(f"   ✅ Target variable: status (0=Non-Stunting, 1=Stunting)")

# 3. Split data training dan testing (80:20)
print("\n3️⃣ Split Data Training dan Testing:")
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)

print(f"   ✅ Data training: {X_train.shape[0]} sampel ({X_train.shape[0]/len(X)*100:.1f}%)")
print(f"   ✅ Data testing: {X_test.shape[0]} sampel ({X_test.shape[0]/len(X)*100:.1f}%)")
print(f"   ✅ Distribusi kelas training: {np.bincount(y_train)}")
print(f"   ✅ Distribusi kelas testing: {np.bincount(y_test)}")

# 4. Standardisasi fitur numerik untuk performa optimal
print("\n4️⃣ Standardisasi Fitur Numerik:")
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

print(f"   ✅ Fitur numerik telah distandardisasi")
print(f"   ✅ Mean setelah scaling: {X_train_scaled.mean():.6f}")
print(f"   ✅ Std setelah scaling: {X_train_scaled.std():.6f}")

print("\n✅ Persiapan data untuk ML selesai!")
print(f"📊 Siap untuk training 3 model: Decision Tree, KNN, Naive Bayes")

print("🎯 Training dan Evaluasi 3 Model Machine Learning...")
print("="*70)

# Dictionary untuk menyimpan hasil semua model
models_results = {}

# 1. DECISION TREE CLASSIFIER
print("\n1️⃣ TRAINING DECISION TREE CLASSIFIER")
print("-" * 50)

start_time = time.time()
dt_model = DecisionTreeClassifier(random_state=42, max_depth=10, min_samples_split=5)
dt_model.fit(X_train_scaled, y_train)
dt_train_time = time.time() - start_time

# Prediksi dan evaluasi
dt_pred = dt_model.predict(X_test_scaled)
dt_accuracy = accuracy_score(y_test, dt_pred)
dt_precision = precision_score(y_test, dt_pred, average='weighted')
dt_recall = recall_score(y_test, dt_pred, average='weighted')
dt_f1 = f1_score(y_test, dt_pred, average='weighted')

models_results['Decision Tree'] = {
    'model': dt_model,
    'predictions': dt_pred,
    'accuracy': dt_accuracy,
    'precision': dt_precision,
    'recall': dt_recall,
    'f1_score': dt_f1,
    'training_time': dt_train_time
}

print(f"   ✅ Training selesai dalam {dt_train_time:.4f} detik")
print(f"   📊 Accuracy: {dt_accuracy:.4f} ({dt_accuracy*100:.2f}%)")
print(f"   📊 Precision: {dt_precision:.4f}")
print(f"   📊 Recall: {dt_recall:.4f}")
print(f"   📊 F1-Score: {dt_f1:.4f}")

# 2. K-NEAREST NEIGHBORS (KNN)
print("\n2️⃣ TRAINING K-NEAREST NEIGHBORS (KNN)")
print("-" * 50)

start_time = time.time()
knn_model = KNeighborsClassifier(n_neighbors=5, weights='distance')
knn_model.fit(X_train_scaled, y_train)
knn_train_time = time.time() - start_time

# Prediksi dan evaluasi
knn_pred = knn_model.predict(X_test_scaled)
knn_accuracy = accuracy_score(y_test, knn_pred)
knn_precision = precision_score(y_test, knn_pred, average='weighted')
knn_recall = recall_score(y_test, knn_pred, average='weighted')
knn_f1 = f1_score(y_test, knn_pred, average='weighted')

models_results['KNN'] = {
    'model': knn_model,
    'predictions': knn_pred,
    'accuracy': knn_accuracy,
    'precision': knn_precision,
    'recall': knn_recall,
    'f1_score': knn_f1,
    'training_time': knn_train_time
}

print(f"   ✅ Training selesai dalam {knn_train_time:.4f} detik")
print(f"   📊 Accuracy: {knn_accuracy:.4f} ({knn_accuracy*100:.2f}%)")
print(f"   📊 Precision: {knn_precision:.4f}")
print(f"   📊 Recall: {knn_recall:.4f}")
print(f"   📊 F1-Score: {knn_f1:.4f}")

# 3. GAUSSIAN NAIVE BAYES
print("\n3️⃣ TRAINING GAUSSIAN NAIVE BAYES")
print("-" * 50)

start_time = time.time()
nb_model = GaussianNB()
nb_model.fit(X_train_scaled, y_train)
nb_train_time = time.time() - start_time

# Prediksi dan evaluasi
nb_pred = nb_model.predict(X_test_scaled)
nb_accuracy = accuracy_score(y_test, nb_pred)
nb_precision = precision_score(y_test, nb_pred, average='weighted')
nb_recall = recall_score(y_test, nb_pred, average='weighted')
nb_f1 = f1_score(y_test, nb_pred, average='weighted')

models_results['Naive Bayes'] = {
    'model': nb_model,
    'predictions': nb_pred,
    'accuracy': nb_accuracy,
    'precision': nb_precision,
    'recall': nb_recall,
    'f1_score': nb_f1,
    'training_time': nb_train_time
}

print(f"   ✅ Training selesai dalam {nb_train_time:.4f} detik")
print(f"   📊 Accuracy: {nb_accuracy:.4f} ({nb_accuracy*100:.2f}%)")
print(f"   📊 Precision: {nb_precision:.4f}")
print(f"   📊 Recall: {nb_recall:.4f}")
print(f"   📊 F1-Score: {nb_f1:.4f}")

print("\n✅ Training semua model selesai!")
print("📊 Siap untuk evaluasi dan pemilihan model terbaik")

print("📊 Evaluasi dan Pemilihan Model Terbaik...")
print("="*70)

# 1. Tabel perbandingan performa semua model
print("\n1️⃣ PERBANDINGAN PERFORMA MODEL")
print("-" * 50)

comparison_data = []
for model_name, results in models_results.items():
    comparison_data.append([
        model_name,
        f"{results['accuracy']:.4f}",
        f"{results['precision']:.4f}",
        f"{results['recall']:.4f}",
        f"{results['f1_score']:.4f}",
        f"{results['training_time']:.4f}s"
    ])

headers = ['Model', 'Accuracy', 'Precision', 'Recall', 'F1-Score', 'Training Time']
print(tabulate(comparison_data, headers=headers, tablefmt='grid', stralign='center'))

# 2. Pemilihan model terbaik berdasarkan F1-Score
print("\n2️⃣ PEMILIHAN MODEL TERBAIK")
print("-" * 40)

best_model_name = max(models_results.keys(), key=lambda x: models_results[x]['f1_score'])
best_model = models_results[best_model_name]['model']
best_predictions = models_results[best_model_name]['predictions']

print(f"🏆 MODEL TERBAIK: {best_model_name}")
print(f"   📊 F1-Score: {models_results[best_model_name]['f1_score']:.4f}")
print(f"   📊 Accuracy: {models_results[best_model_name]['accuracy']:.4f} ({models_results[best_model_name]['accuracy']*100:.2f}%)")
print(f"   ⚡ Training Time: {models_results[best_model_name]['training_time']:.4f} detik")

# 3. Alasan pemilihan model
print("\n3️⃣ ALASAN PEMILIHAN MODEL")
print("-" * 30)
print(f"✅ {best_model_name} dipilih karena:")
print(f"   • F1-Score tertinggi: {models_results[best_model_name]['f1_score']:.4f}")
print(f"   • Balanced performance antara precision dan recall")
print(f"   • Waktu training yang efisien: {models_results[best_model_name]['training_time']:.4f} detik")
print(f"   • Cocok untuk dataset stunting dengan class imbalance")

# 4. Ranking semua model
print("\n4️⃣ RANKING MODEL BERDASARKAN F1-SCORE")
print("-" * 40)
sorted_models = sorted(models_results.items(), key=lambda x: x[1]['f1_score'], reverse=True)
for i, (model_name, results) in enumerate(sorted_models, 1):
    print(f"   {i}. {model_name}: F1-Score = {results['f1_score']:.4f}")

print("\n✅ Evaluasi dan pemilihan model terbaik selesai!")
print(f"🎯 Model {best_model_name} akan digunakan untuk analisis lanjutan")

print("🎯 Analisis Confusion Matrix dan Classification Report...")
print("="*70)

# 1. Confusion Matrix untuk semua model
print("\n1️⃣ CONFUSION MATRIX UNTUK SEMUA MODEL")
print("-" * 50)

# Visualisasi confusion matrix untuk semua model
fig, axes = plt.subplots(1, 3, figsize=(18, 5))
fig.suptitle('🎯 Confusion Matrix - Perbandingan 3 Model ML', fontsize=16, fontweight='bold')

model_names = list(models_results.keys())
for i, (model_name, results) in enumerate(models_results.items()):
    cm = confusion_matrix(y_test, results['predictions'])

    # Plot confusion matrix
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=axes[i],
                xticklabels=['Non-Stunting', 'Stunting'],
                yticklabels=['Non-Stunting', 'Stunting'])
    axes[i].set_title(f'{model_name}\nAccuracy: {results["accuracy"]:.3f}', fontweight='bold')
    axes[i].set_xlabel('Prediksi')
    axes[i].set_ylabel('Aktual')

plt.tight_layout()
plt.show()

# 2. Classification Report untuk model terbaik
print(f"\n2️⃣ CLASSIFICATION REPORT - {best_model_name.upper()}")
print("-" * 50)

class_report = classification_report(y_test, best_predictions, output_dict=True)
print(classification_report(y_test, best_predictions, target_names=['Non-Stunting', 'Stunting']))

# Tabel classification report yang lebih rapi
report_data = []
for class_name in ['Non-Stunting', 'Stunting']:
    class_key = '0' if class_name == 'Non-Stunting' else '1'
    report_data.append([
        class_name,
        f"{class_report[class_key]['precision']:.4f}",
        f"{class_report[class_key]['recall']:.4f}",
        f"{class_report[class_key]['f1-score']:.4f}",
        f"{int(class_report[class_key]['support'])}"
    ])

# Tambahkan weighted average
report_data.append([
    'Weighted Avg',
    f"{class_report['weighted avg']['precision']:.4f}",
    f"{class_report['weighted avg']['recall']:.4f}",
    f"{class_report['weighted avg']['f1-score']:.4f}",
    f"{int(class_report['weighted avg']['support'])}"
])

headers = ['Kelas', 'Precision', 'Recall', 'F1-Score', 'Support']
print(tabulate(report_data, headers=headers, tablefmt='grid', stralign='center'))

# 3. Interpretasi hasil confusion matrix model terbaik
print(f"\n3️⃣ INTERPRETASI HASIL {best_model_name.upper()}")
print("-" * 50)

cm_best = confusion_matrix(y_test, best_predictions)
tn, fp, fn, tp = cm_best.ravel()

print(f"📊 Confusion Matrix Breakdown:")
print(f"   ✅ True Negative (TN): {tn} - Non-stunting diprediksi benar")
print(f"   ❌ False Positive (FP): {fp} - Non-stunting diprediksi stunting")
print(f"   ❌ False Negative (FN): {fn} - Stunting diprediksi non-stunting")
print(f"   ✅ True Positive (TP): {tp} - Stunting diprediksi benar")

print(f"\n📈 Metrik Tambahan:")
sensitivity = tp / (tp + fn)  # Recall untuk kelas Stunting
specificity = tn / (tn + fp)  # Recall untuk kelas Non-Stunting
print(f"   • Sensitivity (Recall Stunting): {sensitivity:.4f} ({sensitivity*100:.1f}%)")
print(f"   • Specificity (Recall Non-Stunting): {specificity:.4f} ({specificity*100:.1f}%)")

print(f"\n💡 Interpretasi Klinis:")
print(f"   • Model dapat mendeteksi {sensitivity*100:.1f}% kasus stunting dengan benar")
print(f"   • Model dapat mengidentifikasi {specificity*100:.1f}% kasus non-stunting dengan benar")
print(f"   • Tingkat kesalahan prediksi: {(fp+fn)/(tn+fp+fn+tp)*100:.1f}%")

print("\n✅ Analisis confusion matrix selesai!")
print("📊 Model siap untuk analisis regional dan prediksi")

print("🗺️ Analisis Regional Stunting Kabupaten/Kota Jawa Barat...")
print("="*80)

# 1. Analisis komprehensif per kabupaten/kota
print("\n1️⃣ ANALISIS KOMPREHENSIF PER KABUPATEN/KOTA")
print("-" * 60)

# Hitung statistik detail per daerah menggunakan semua field dataset
regional_detailed = df_clean.groupby('nama_kabupaten_kota').agg({
    'status': ['count', lambda x: (x == 'Stunting').sum()],
    'tinggi_badan_cm': ['mean', 'std'],
    'berat_badan_kg': ['mean', 'std'],
    'usia_balita_bulan': ['mean', 'std'],
    'bmi': ['mean', 'std'],
    'jenis_kelamin': lambda x: (x == 'Laki-laki').sum(),
    'tahun': lambda x: x.mode().iloc[0] if len(x.mode()) > 0 else x.iloc[0]
}).round(2)

# Flatten column names
regional_detailed.columns = [
    'Total_Balita', 'Jumlah_Stunting', 'Rata_Tinggi', 'Std_Tinggi',
    'Rata_Berat', 'Std_Berat', 'Rata_Usia', 'Std_Usia',
    'Rata_BMI', 'Std_BMI', 'Jumlah_Laki', 'Tahun_Dominan'
]

# Hitung persentase dan tambahan metrics
regional_detailed['Persentase_Stunting'] = (regional_detailed['Jumlah_Stunting'] / regional_detailed['Total_Balita'] * 100).round(2)
regional_detailed['Persentase_Laki'] = (regional_detailed['Jumlah_Laki'] / regional_detailed['Total_Balita'] * 100).round(2)

# Kategorisasi risiko berdasarkan persentase stunting
def kategorikan_risiko(pct):
    if pct >= 30:
        return 'Tinggi'
    elif pct >= 20:
        return 'Sedang'
    else:
        return 'Rendah'

regional_detailed['Kategori_Risiko'] = regional_detailed['Persentase_Stunting'].apply(kategorikan_risiko)

# Sort berdasarkan persentase stunting
regional_detailed = regional_detailed.sort_values('Persentase_Stunting', ascending=False)

print(f"📊 Total kabupaten/kota dianalisis: {len(regional_detailed)}")
print(f"📊 Rata-rata stunting Jawa Barat: {regional_detailed['Persentase_Stunting'].mean():.2f}%")
print(f"📊 Standar deviasi: {regional_detailed['Persentase_Stunting'].std():.2f}%")

# 2. Top 10 kabupaten/kota dengan stunting tertinggi
print("\n2️⃣ TOP 10 KABUPATEN/KOTA STUNTING TERTINGGI")
print("-" * 60)

highest_data = []
for i, (region, data) in enumerate(regional_detailed.head(10).iterrows(), 1):
    highest_data.append([
        i,
        region[:25] + '...' if len(region) > 25 else region,
        f"{data['Persentase_Stunting']:.1f}%",
        f"{int(data['Jumlah_Stunting'])}/{int(data['Total_Balita'])}",
        f"{data['Rata_Tinggi']:.1f} cm",
        f"{data['Rata_BMI']:.1f}",
        data['Kategori_Risiko']
    ])

headers_highest = ['Rank', 'Kabupaten/Kota', 'Stunting %', 'Stunting/Total', 'Rata Tinggi', 'Rata BMI', 'Risiko']
print(tabulate(highest_data, headers=headers_highest, tablefmt='grid', stralign='center'))

# 3. Top 10 kabupaten/kota dengan stunting terendah
print("\n3️⃣ TOP 10 KABUPATEN/KOTA STUNTING TERENDAH")
print("-" * 60)

lowest_data = []
for i, (region, data) in enumerate(regional_detailed.tail(10).iterrows(), 1):
    lowest_data.append([
        i,
        region[:25] + '...' if len(region) > 25 else region,
        f"{data['Persentase_Stunting']:.1f}%",
        f"{int(data['Jumlah_Stunting'])}/{int(data['Total_Balita'])}",
        f"{data['Rata_Tinggi']:.1f} cm",
        f"{data['Rata_BMI']:.1f}",
        data['Kategori_Risiko']
    ])

headers_lowest = ['Rank', 'Kabupaten/Kota', 'Stunting %', 'Stunting/Total', 'Rata Tinggi', 'Rata BMI', 'Risiko']
print(tabulate(lowest_data, headers=headers_lowest, tablefmt='grid', stralign='center'))

# 4. Kategorisasi risiko
print("\n4️⃣ KATEGORISASI RISIKO STUNTING")
print("-" * 40)

risk_summary = regional_detailed['Kategori_Risiko'].value_counts()
for risk_level in ['Tinggi', 'Sedang', 'Rendah']:
    if risk_level in risk_summary.index:
        count = risk_summary[risk_level]
        pct = (count / len(regional_detailed) * 100)
        icon = '🔴' if risk_level == 'Tinggi' else '🟡' if risk_level == 'Sedang' else '🟢'
        print(f"   {icon} Risiko {risk_level}: {count} daerah ({pct:.1f}%)")

        # Tampilkan beberapa contoh daerah
        regions = regional_detailed[regional_detailed['Kategori_Risiko'] == risk_level].index[:5]
        for region in regions:
            pct = regional_detailed.loc[region, 'Persentase_Stunting']
            print(f"   • {region}: {pct:.1f}%")
        if len(regions) > 5:
            print(f"   • ... dan {len(regions)-5} daerah lainnya")

print("\n✅ Analisis regional stunting selesai!")
print("📊 Data siap untuk tabel analisis komprehensif")

print("📋 JABAR STUNTING ANALYZER - Membuat Tabel Analisis Komprehensif...")
print("="*70)

# 1. TABEL AGREGASI UTAMA - Format mengikuti referensi
print("\n1️⃣ TABEL AGREGASI STUNTING PER KABUPATEN/KOTA")
print("-" * 60)

# Buat tabel agregasi yang mengikuti format referensi
tabel_agregasi = df_clean.groupby(['nama_kabupaten_kota', 'tahun']).agg({
    'status': ['count', lambda x: (x == 'Stunting').sum()],
    'tinggi_badan_cm': ['mean', 'median', 'std', 'min', 'max'],
    'berat_badan_kg': ['mean', 'median', 'std', 'min', 'max'],
    'usia_balita_bulan': ['mean', 'median', 'std'],
    'bmi': ['mean', 'median', 'std'],
    'jenis_kelamin': lambda x: (x == 'Laki-laki').sum()
}).round(2)

# Flatten dan rename columns untuk kemudahan
tabel_agregasi.columns = [
    'Total_Balita', 'Stunting_Count', 'Tinggi_Mean', 'Tinggi_Median', 'Tinggi_Std', 'Tinggi_Min', 'Tinggi_Max',
    'Berat_Mean', 'Berat_Median', 'Berat_Std', 'Berat_Min', 'Berat_Max',
    'Usia_Mean', 'Usia_Median', 'Usia_Std', 'BMI_Mean', 'BMI_Median', 'BMI_Std', 'Laki_Count'
]

# Tambahkan kolom perhitungan
tabel_agregasi['Stunting_Pct'] = (tabel_agregasi['Stunting_Count'] / tabel_agregasi['Total_Balita'] * 100).round(2)
tabel_agregasi['Laki_Pct'] = (tabel_agregasi['Laki_Count'] / tabel_agregasi['Total_Balita'] * 100).round(2)
tabel_agregasi['Non_Stunting_Count'] = tabel_agregasi['Total_Balita'] - tabel_agregasi['Stunting_Count']

# Reset index untuk tampilan yang lebih baik
tabel_agregasi = tabel_agregasi.reset_index()

# Tampilkan 15 baris pertama dari tabel agregasi
print("📊 Contoh Tabel Agregasi (15 baris pertama):")
display_cols = ['nama_kabupaten_kota', 'tahun', 'Total_Balita', 'Stunting_Count', 'Stunting_Pct',
                'Tinggi_Mean', 'Berat_Mean', 'BMI_Mean']
print(tabulate(tabel_agregasi[display_cols].head(15), headers=display_cols, tablefmt='grid', floatfmt='.2f'))

# 2. TABEL SUMMARY STATISTIK DESKRIPTIF
print("\n2️⃣ TABEL SUMMARY STATISTIK DESKRIPTIF")
print("-" * 50)

# Buat summary statistik untuk semua variabel numerik
summary_stats = df_clean[['tinggi_badan_cm', 'berat_badan_kg', 'usia_balita_bulan', 'bmi']].describe().round(2)
summary_stats.loc['variance'] = df_clean[['tinggi_badan_cm', 'berat_badan_kg', 'usia_balita_bulan', 'bmi']].var().round(2)

print("📊 Statistik Deskriptif Variabel Numerik:")
print(tabulate(summary_stats, headers=['Tinggi (cm)', 'Berat (kg)', 'Usia (bulan)', 'BMI'], tablefmt='grid', floatfmt='.2f'))

# 3. TABEL DISTRIBUSI STUNTING PER KARAKTERISTIK
print("\n3️⃣ TABEL DISTRIBUSI STUNTING PER KARAKTERISTIK")
print("-" * 55)

# Distribusi berdasarkan jenis kelamin
gender_table = pd.crosstab(df_clean['jenis_kelamin'], df_clean['status'], margins=True)
gender_table['Stunting_Pct'] = (gender_table['Stunting'] / gender_table['All'] * 100).round(2)

print("👶 Distribusi Stunting berdasarkan Jenis Kelamin:")
print(tabulate(gender_table, headers=['Non-Stunting', 'Stunting', 'Total', 'Stunting %'], tablefmt='grid'))

# Distribusi berdasarkan kategori usia
age_table = pd.crosstab(df_clean['kategori_usia'], df_clean['status'], margins=True)
age_table['Stunting_Pct'] = (age_table['Stunting'] / age_table['All'] * 100).round(2)

print("\n📅 Distribusi Stunting berdasarkan Kategori Usia:")
print(tabulate(age_table, headers=['Non-Stunting', 'Stunting', 'Total', 'Stunting %'], tablefmt='grid'))

# Distribusi berdasarkan kategori BMI
bmi_table = pd.crosstab(df_clean['kategori_bmi'], df_clean['status'], margins=True)
bmi_table['Stunting_Pct'] = (bmi_table['Stunting'] / bmi_table['All'] * 100).round(2)

print("\n⚖️ Distribusi Stunting berdasarkan Kategori BMI:")
print(tabulate(bmi_table, headers=['Non-Stunting', 'Stunting', 'Total', 'Stunting %'], tablefmt='grid'))

# 4. TABEL RANKING DAERAH PRIORITAS
print("\n4️⃣ TABEL RANKING DAERAH PRIORITAS INTERVENSI")
print("-" * 55)

# Ambil data dari analisis regional sebelumnya
priority_table = regional_detailed[['Total_Balita', 'Jumlah_Stunting', 'Persentase_Stunting',
                                   'Rata_Tinggi', 'Rata_BMI', 'Kategori_Risiko']].head(15)

priority_data = []
for i, (region, data) in enumerate(priority_table.iterrows(), 1):
    priority_data.append([
        i,
        region[:30] + '...' if len(region) > 30 else region,
        int(data['Total_Balita']),
        int(data['Jumlah_Stunting']),
        f"{data['Persentase_Stunting']:.1f}%",
        f"{data['Rata_Tinggi']:.1f}",
        f"{data['Rata_BMI']:.1f}",
        data['Kategori_Risiko']
    ])

headers_priority = ['Rank', 'Kabupaten/Kota', 'Total', 'Stunting', 'Stunting %', 'Rata Tinggi', 'Rata BMI', 'Risiko']
print("🎯 Top 15 Daerah Prioritas Intervensi:")
print(tabulate(priority_data, headers=headers_priority, tablefmt='grid', stralign='center'))

print("\n✅ Tabel analisis komprehensif selesai!")
print("📊 Semua tabel mengikuti format referensi dengan agregasi yang tepat")

# 5. EXPORT TABEL KE EXCEL
print("\n5️⃣ EXPORT TABEL ANALISIS KE EXCEL")
print("-" * 50)

try:
    from datetime import datetime
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f'JABAR_STUNTING_ANALYZER_{timestamp}.xlsx'

    # Buat Excel writer object
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:

        # Sheet 1: Regional Analysis
        regional_export = regional_detailed.copy()
        regional_export.reset_index(inplace=True)
        regional_export.to_excel(writer, sheet_name='Regional_Analysis', index=False)

        # Sheet 2: Aggregation Table
        tabel_agregasi.to_excel(writer, sheet_name='Aggregation_Table', index=True)

        # Sheet 3: Summary Statistics
        summary_stats.to_excel(writer, sheet_name='Summary_Statistics', index=True)

        # Sheet 4: Distribution Tables
        gender_table.to_excel(writer, sheet_name='Gender_Distribution', index=True)
        age_table.to_excel(writer, sheet_name='Age_Distribution', index=True)
        bmi_table.to_excel(writer, sheet_name='BMI_Distribution', index=True)

        # Sheet 5: Model Results
        model_results_df = pd.DataFrame({
            'Model': list(models_results.keys()),
            'Accuracy': [results['accuracy'] for results in models_results.values()],
            'Precision': [results['precision'] for results in models_results.values()],
            'Recall': [results['recall'] for results in models_results.values()],
            'F1_Score': [results['f1_score'] for results in models_results.values()],
            'Training_Time': [results['training_time'] for results in models_results.values()]
        })
        model_results_df.to_excel(writer, sheet_name='Model_Results', index=False)

        # Sheet 6: Priority Ranking
        priority_df = pd.DataFrame(priority_data, columns=headers_priority)
        priority_df.to_excel(writer, sheet_name='Priority_Ranking', index=False)

    print(f"✅ Export berhasil! File disimpan: {filename}")
    print(f"📁 File berisi 6 sheets:")
    print(f"   • Regional_Analysis: Analisis per kabupaten/kota")
    print(f"   • Aggregation_Table: Tabel agregasi lengkap")
    print(f"   • Summary_Statistics: Statistik deskriptif")
    print(f"   • Gender/Age/BMI_Distribution: Distribusi karakteristik")
    print(f"   • Model_Results: Performa model ML")
    print(f"   • Priority_Ranking: Ranking daerah prioritas")

except Exception as e:
    print(f"❌ Error saat export: {str(e)}")
    print(f"💡 Pastikan library openpyxl terinstall: pip install openpyxl")

print("📊 Membuat Visualisasi Data dan Hasil Analisis...")
print("="*70)

# Set style untuk visualisasi yang profesional
plt.style.use('default')
sns.set_palette("husl")

# 1. VISUALISASI DISTRIBUSI STUNTING PER DAERAH
print("\n1️⃣ Visualisasi Distribusi Stunting per Daerah")
print("-" * 50)

# Top 15 daerah dengan stunting tertinggi
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 12))

# Plot 1: Top 15 stunting tertinggi
top_15_highest = regional_detailed.head(15)
colors_high = ['#ff4444' if x >= 30 else '#ff8800' if x >= 20 else '#44aa44' for x in top_15_highest['Persentase_Stunting']]

bars1 = ax1.bar(range(len(top_15_highest)), top_15_highest['Persentase_Stunting'], color=colors_high)
ax1.set_title('🔴 Top 15 Kabupaten/Kota dengan Stunting Tertinggi', fontsize=14, fontweight='bold')
ax1.set_ylabel('Persentase Stunting (%)', fontweight='bold')
ax1.set_xticks(range(len(top_15_highest)))
ax1.set_xticklabels([name[:15] + '...' if len(name) > 15 else name for name in top_15_highest.index],
                    rotation=45, ha='right')
ax1.grid(axis='y', alpha=0.3)

# Tambahkan nilai di atas bar
for i, bar in enumerate(bars1):
    height = bar.get_height()
    ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5, f'{height:.1f}%',
             ha='center', va='bottom', fontweight='bold')

# Plot 2: Top 15 stunting terendah
top_15_lowest = regional_detailed.tail(15)
colors_low = ['#44aa44' for _ in top_15_lowest['Persentase_Stunting']]

bars2 = ax2.bar(range(len(top_15_lowest)), top_15_lowest['Persentase_Stunting'], color=colors_low)
ax2.set_title('🟢 Top 15 Kabupaten/Kota dengan Stunting Terendah', fontsize=14, fontweight='bold')
ax2.set_ylabel('Persentase Stunting (%)', fontweight='bold')
ax2.set_xlabel('Kabupaten/Kota', fontweight='bold')
ax2.set_xticks(range(len(top_15_lowest)))
ax2.set_xticklabels([name[:15] + '...' if len(name) > 15 else name for name in top_15_lowest.index],
                    rotation=45, ha='right')
ax2.grid(axis='y', alpha=0.3)

# Tambahkan nilai di atas bar
for i, bar in enumerate(bars2):
    height = bar.get_height()
    ax2.text(bar.get_x() + bar.get_width()/2., height + 0.2, f'{height:.1f}%',
             ha='center', va='bottom', fontweight='bold')

plt.tight_layout()
plt.show()

# 2. VISUALISASI PERBANDINGAN KARAKTERISTIK
print("\n2️⃣ Visualisasi Perbandingan Karakteristik Balita")
print("-" * 50)

fig, axes = plt.subplots(2, 2, figsize=(16, 12))
fig.suptitle('📊 JABAR STUNTING ANALYZER - Perbandingan Karakteristik Balita: Stunting vs Non-Stunting', fontsize=16, fontweight='bold')

# Plot 1: Distribusi Tinggi Badan
stunting_data = df_clean[df_clean['status'] == 'Stunting']['tinggi_badan_cm']
non_stunting_data = df_clean[df_clean['status'] == 'Non-Stunting']['tinggi_badan_cm']

axes[0,0].hist([non_stunting_data, stunting_data], bins=20, alpha=0.7,
               label=['Non-Stunting', 'Stunting'], color=['#44aa44', '#ff4444'])
axes[0,0].set_title('Distribusi Tinggi Badan', fontweight='bold')
axes[0,0].set_xlabel('Tinggi Badan (cm)')
axes[0,0].set_ylabel('Frekuensi')
axes[0,0].legend()
axes[0,0].grid(alpha=0.3)

# Plot 2: Distribusi Berat Badan
stunting_weight = df_clean[df_clean['status'] == 'Stunting']['berat_badan_kg']
non_stunting_weight = df_clean[df_clean['status'] == 'Non-Stunting']['berat_badan_kg']

axes[0,1].hist([non_stunting_weight, stunting_weight], bins=20, alpha=0.7,
               label=['Non-Stunting', 'Stunting'], color=['#44aa44', '#ff4444'])
axes[0,1].set_title('Distribusi Berat Badan', fontweight='bold')
axes[0,1].set_xlabel('Berat Badan (kg)')
axes[0,1].set_ylabel('Frekuensi')
axes[0,1].legend()
axes[0,1].grid(alpha=0.3)

# Plot 3: Distribusi BMI
stunting_bmi = df_clean[df_clean['status'] == 'Stunting']['bmi']
non_stunting_bmi = df_clean[df_clean['status'] == 'Non-Stunting']['bmi']

axes[1,0].hist([non_stunting_bmi, stunting_bmi], bins=20, alpha=0.7,
               label=['Non-Stunting', 'Stunting'], color=['#44aa44', '#ff4444'])
axes[1,0].set_title('Distribusi BMI', fontweight='bold')
axes[1,0].set_xlabel('BMI')
axes[1,0].set_ylabel('Frekuensi')
axes[1,0].legend()
axes[1,0].grid(alpha=0.3)

# Plot 4: Distribusi Usia
stunting_age = df_clean[df_clean['status'] == 'Stunting']['usia_balita_bulan']
non_stunting_age = df_clean[df_clean['status'] == 'Non-Stunting']['usia_balita_bulan']

axes[1,1].hist([non_stunting_age, stunting_age], bins=20, alpha=0.7,
               label=['Non-Stunting', 'Stunting'], color=['#44aa44', '#ff4444'])
axes[1,1].set_title('Distribusi Usia', fontweight='bold')
axes[1,1].set_xlabel('Usia (bulan)')
axes[1,1].set_ylabel('Frekuensi')
axes[1,1].legend()
axes[1,1].grid(alpha=0.3)

plt.tight_layout()
plt.show()

print("\n✅ Visualisasi distribusi dan perbandingan selesai!")
print("📊 Grafik mendukung analisis dan mudah dipahami")

print("🗺️ Membuat Peta Interaktif Stunting Jawa Barat...")
print("="*60)

# 1. PERSIAPAN DATA UNTUK PETA
print("\n1️⃣ Persiapan Data Geografis")
print("-" * 40)

# Buat dataset untuk peta dengan koordinat estimasi kabupaten/kota Jawa Barat
# Koordinat ini adalah estimasi untuk keperluan visualisasi
koordinat_jabar = {
    'Kab. Bandung': [-6.9175, 107.6191],
    'Kab. Bandung Barat': [-6.8186, 107.4917],
    'Kab. Bekasi': [-6.2383, 107.0011],
    'Kab. Bogor': [-6.5971, 106.8060],
    'Kab. Ciamis': [-7.3257, 108.3534],
    'Kab. Cianjur': [-6.8200, 107.1425],
    'Kab. Cirebon': [-6.7063, 108.5571],
    'Kab. Garut': [-7.2253, 107.8986],
    'Kab. Indramayu': [-6.3274, 108.3199],
    'Kab. Karawang': [-6.3015, 107.3020],
    'Kab. Kuningan': [-6.9759, 108.4836],
    'Kab. Majalengka': [-6.8360, 108.2277],
    'Kab. Pangandaran': [-7.6867, 108.6500],
    'Kab. Purwakarta': [-6.5569, 107.4431],
    'Kab. Subang': [-6.5627, 107.7539],
    'Kab. Sukabumi': [-6.9278, 106.9571],
    'Kab. Sumedang': [-6.8387, 107.9214],
    'Kab. Tasikmalaya': [-7.3506, 108.2172],
    'Kota Bandung': [-6.9175, 107.6191],
    'Kota Banjar': [-7.3729, 108.5389],
    'Kota Bekasi': [-6.2383, 107.0011],
    'Kota Bogor': [-6.5971, 106.8060],
    'Kota Cimahi': [-6.8723, 107.5425],
    'Kota Cirebon': [-6.7320, 108.5520],
    'Kota Depok': [-6.4025, 106.7942],
    'Kota Sukabumi': [-6.9278, 106.9571],
    'Kota Tasikmalaya': [-7.3274, 108.2207]
}

# Gabungkan data stunting dengan koordinat
map_data = []
for region in regional_detailed.index:
    if region in koordinat_jabar:
        lat, lon = koordinat_jabar[region]
        stunting_pct = regional_detailed.loc[region, 'Persentase_Stunting']
        total_balita = regional_detailed.loc[region, 'Total_Balita']
        stunting_count = regional_detailed.loc[region, 'Jumlah_Stunting']
        kategori_risiko = regional_detailed.loc[region, 'Kategori_Risiko']
        rata_tinggi = regional_detailed.loc[region, 'Rata_Tinggi']
        rata_bmi = regional_detailed.loc[region, 'Rata_BMI']

        map_data.append({
            'region': region,
            'latitude': lat,
            'longitude': lon,
            'stunting_pct': stunting_pct,
            'total_balita': total_balita,
            'stunting_count': stunting_count,
            'kategori_risiko': kategori_risiko,
            'rata_tinggi': rata_tinggi,
            'rata_bmi': rata_bmi
        })

map_df = pd.DataFrame(map_data)
print(f"✅ Data geografis siap: {len(map_df)} daerah dengan koordinat")

# 2. MEMBUAT PETA INTERAKTIF
print("\n2️⃣ Membuat Peta Interaktif")
print("-" * 40)

# Inisialisasi peta dengan center di Jawa Barat
center_lat = -6.9175
center_lon = 107.6191
m = folium.Map(
    location=[center_lat, center_lon],
    zoom_start=9,
    tiles='OpenStreetMap'
)

# Fungsi untuk menentukan warna berdasarkan kategori risiko
def get_color(kategori_risiko):
    if kategori_risiko == 'Tinggi':
        return 'red'
    elif kategori_risiko == 'Sedang':
        return 'orange'
    else:
        return 'green'

# Fungsi untuk menentukan ukuran marker berdasarkan persentase stunting
def get_radius(stunting_pct):
    return max(5, min(25, stunting_pct * 0.8))  # Radius antara 5-25

# Tambahkan marker untuk setiap daerah
for _, row in map_df.iterrows():
    # Buat popup dengan informasi detail
    popup_html = f"""
    <div style="font-family: Arial; width: 250px;">
        <h4 style="color: {get_color(row['kategori_risiko'])}; margin-bottom: 10px;">
            📍 {row['region']}
        </h4>
        <hr style="margin: 5px 0;">
        <p><b>🎯 Stunting:</b> {row['stunting_pct']:.1f}% ({int(row['stunting_count'])}/{int(row['total_balita'])} balita)</p>
        <p><b>⚠️ Kategori Risiko:</b> <span style="color: {get_color(row['kategori_risiko'])};"><b>{row['kategori_risiko']}</b></span></p>
        <p><b>📏 Rata-rata Tinggi:</b> {row['rata_tinggi']:.1f} cm</p>
        <p><b>⚖️ Rata-rata BMI:</b> {row['rata_bmi']:.1f}</p>
    </div>
    """

    # Tambahkan circle marker
    folium.CircleMarker(
        location=[row['latitude'], row['longitude']],
        radius=get_radius(row['stunting_pct']),
        popup=folium.Popup(popup_html, max_width=300),
        color='black',
        weight=2,
        fillColor=get_color(row['kategori_risiko']),
        fillOpacity=0.7,
        tooltip=f"{row['region']}: {row['stunting_pct']:.1f}% stunting"
    ).add_to(m)

# Tambahkan legend
legend_html = '''
<div style="position: fixed;
            bottom: 50px; left: 50px; width: 200px; height: 120px;
            background-color: white; border:2px solid grey; z-index:9999;
            font-size:14px; padding: 10px">
<h4>🗺️ Legend Stunting</h4>
<p><i class="fa fa-circle" style="color:red"></i> Risiko Tinggi (≥30%)</p>
<p><i class="fa fa-circle" style="color:orange"></i> Risiko Sedang (20-29%)</p>
<p><i class="fa fa-circle" style="color:green"></i> Risiko Rendah (<20%)</p>
<p><small>Ukuran lingkaran = % stunting</small></p>
</div>
'''
m.get_root().html.add_child(folium.Element(legend_html))

# Tambahkan title
title_html = '''
<h3 align="center" style="font-size:20px"><b>🗺️ JABAR STUNTING ANALYZER - Peta Interaktif Stunting Balita Jawa Barat</b></h3>
'''
m.get_root().html.add_child(folium.Element(title_html))

print("✅ Peta interaktif berhasil dibuat!")
print(f"📊 {len(map_df)} daerah ditampilkan dengan color coding risiko")
print("🖱️ Klik marker untuk melihat detail informasi")

# Tampilkan peta
display(m)

# 3. STATISTIK PETA
print("\n3️⃣ Statistik Distribusi Geografis")
print("-" * 40)

risk_distribution = map_df['kategori_risiko'].value_counts()
print("📊 Distribusi Risiko di Peta:")
for risk, count in risk_distribution.items():
    pct = (count / len(map_df) * 100)
    icon = '🔴' if risk == 'Tinggi' else '🟡' if risk == 'Sedang' else '🟢'
    print(f"   {icon} {risk}: {count} daerah ({pct:.1f}%)")

print(f"\n📍 Hotspot Stunting (Top 5):")
top_5_map = map_df.nlargest(5, 'stunting_pct')
for i, (_, row) in enumerate(top_5_map.iterrows(), 1):
    print(f"   {i}. {row['region']}: {row['stunting_pct']:.1f}%")

print("\n✅ Visualisasi peta interaktif selesai!")
print("🗺️ Peta memberikan perspektif geografis yang valuable untuk stakeholder")

print("📈 Analisis Korelasi Key Variables...")
print("="*50)

# 1. VALIDASI DATA: KORELASI TINGGI VS BERAT
print("\n1️⃣ Validasi Data: Korelasi Tinggi vs Berat Badan")
print("-" * 50)

# Hitung korelasi untuk validasi data
correlation_height_weight = df_clean['tinggi_badan_cm'].corr(df_clean['berat_badan_kg'])
print(f"📊 Korelasi Tinggi-Berat: {correlation_height_weight:.4f}")

# Interpretasi korelasi
if correlation_height_weight > 0.7:
    print(f"   ✅ Korelasi KUAT - Data antropometri valid dan konsisten")
elif correlation_height_weight > 0.5:
    print(f"   ⚠️ Korelasi SEDANG - Data cukup valid dengan beberapa variasi")
else:
    print(f"   ❌ Korelasi LEMAH - Perlu review kualitas data")

# Buat scatter plot sederhana untuk validasi visual
plt.figure(figsize=(10, 6))
colors = {'Stunting': '#ff4444', 'Non-Stunting': '#44aa44'}
for status in df_clean['status'].unique():
    subset = df_clean[df_clean['status'] == status]
    plt.scatter(subset['tinggi_badan_cm'], subset['berat_badan_kg'],
               c=colors[status], label=status, alpha=0.6, s=30)

plt.xlabel('Tinggi Badan (cm)', fontsize=12)
plt.ylabel('Berat Badan (kg)', fontsize=12)
plt.title(f'📊 Validasi Data: Tinggi vs Berat Badan\n(Korelasi: {correlation_height_weight:.3f})', fontsize=14, fontweight='bold')
plt.legend()
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.show()

# 2. ANALISIS BMI BERDASARKAN STATUS STUNTING
print("\n2️⃣ Analisis BMI berdasarkan Status Stunting")
print("-" * 50)

# Hitung statistik BMI
bmi_stunting = df_clean[df_clean['status'] == 'Stunting']['bmi'].mean()
bmi_non_stunting = df_clean[df_clean['status'] == 'Non-Stunting']['bmi'].mean()
bmi_diff = abs(bmi_non_stunting - bmi_stunting)

print(f"📊 Rata-rata BMI Stunting: {bmi_stunting:.2f}")
print(f"📊 Rata-rata BMI Non-Stunting: {bmi_non_stunting:.2f}")
print(f"📊 Selisih BMI: {bmi_diff:.2f}")

# Interpretasi BMI
if bmi_diff > 2:
    print(f"   ✅ Perbedaan BMI SIGNIFIKAN - BMI dapat menjadi indikator stunting")
elif bmi_diff > 1:
    print(f"   ⚠️ Perbedaan BMI SEDANG - BMI cukup informatif")
else:
    print(f"   ❌ Perbedaan BMI KECIL - BMI kurang diskriminatif")

# 3. SUMMARY INSIGHTS UNTUK DECISION MAKING
print("\n3️⃣ Summary Insights untuk Decision Making")
print("-" * 50)

print("💡 KEY INSIGHTS:")
print(f"   • Validitas Data: {'Baik' if correlation_height_weight > 0.6 else 'Perlu Review'}")
print(f"   • BMI sebagai Indikator: {'Efektif' if bmi_diff > 1.5 else 'Kurang Efektif'}")
print(f"   • Konsistensi Antropometri: {'Tinggi' if correlation_height_weight > 0.7 else 'Sedang'}")

print("\n🎯 REKOMENDASI ANALISIS:")
if correlation_height_weight > 0.7 and bmi_diff > 1.5:
    print("   ✅ Data berkualitas tinggi, analisis ML dapat diandalkan")
    print("   ✅ BMI dan tinggi-berat dapat digunakan sebagai fitur utama")
elif correlation_height_weight > 0.5:
    print("   ⚠️ Data cukup baik, perlu validasi tambahan")
    print("   ⚠️ Gunakan multiple features untuk akurasi lebih baik")
else:
    print("   ❌ Perlu review dan cleaning data lebih lanjut")
    print("   ❌ Pertimbangkan pengumpulan data tambahan")

print("\n✅ Analisis Korelasi Key Variables selesai!")
print("📊 Insights siap untuk mendukung decision making")

print("🎯 Kesimpulan dan Rekomendasi Strategis JABAR STUNTING ANALYZER")
print("="*80)

# 1. SUMMARY HASIL MACHINE LEARNING
print("\n1️⃣ SUMMARY HASIL MACHINE LEARNING")
print("-" * 50)

print(f"🤖 MODEL TERBAIK: {best_model_name}")
print(f"   📊 Akurasi: {models_results[best_model_name]['accuracy']*100:.2f}%")
print(f"   📊 F1-Score: {models_results[best_model_name]['f1_score']:.4f}")
print(f"   📊 Precision: {models_results[best_model_name]['precision']:.4f}")
print(f"   📊 Recall: {models_results[best_model_name]['recall']:.4f}")
print(f"   ⚡ Waktu Training: {models_results[best_model_name]['training_time']:.4f} detik")

print(f"\n💡 INTERPRETASI MODEL:")
print(f"   • Model dapat memprediksi stunting dengan akurasi {models_results[best_model_name]['accuracy']*100:.1f}%")
print(f"   • Tingkat kesalahan prediksi: {(1-models_results[best_model_name]['accuracy'])*100:.1f}%")
print(f"   • Model cocok untuk screening awal dan identifikasi risiko")
print(f"   • Performa balanced antara precision dan recall")

# 2. INSIGHTS UTAMA ANALISIS REGIONAL
print("\n2️⃣ INSIGHTS UTAMA ANALISIS REGIONAL")
print("-" * 50)

avg_stunting = regional_detailed['Persentase_Stunting'].mean()
highest_region = regional_detailed.index[0]
lowest_region = regional_detailed.index[-1]
highest_pct = regional_detailed['Persentase_Stunting'].iloc[0]
lowest_pct = regional_detailed['Persentase_Stunting'].iloc[-1]

print(f"📊 STATISTIK KUNCI:")
print(f"   • Rata-rata stunting Jawa Barat: {avg_stunting:.2f}%")
print(f"   • Total kabupaten/kota dianalisis: {len(regional_detailed)}")
print(f"   • Daerah stunting tertinggi: {highest_region} ({highest_pct:.2f}%)")
print(f"   • Daerah stunting terendah: {lowest_region} ({lowest_pct:.2f}%)")
print(f"   • Rentang stunting: {highest_pct - lowest_pct:.2f} poin persentase")

# Hitung distribusi risiko
risk_counts = regional_detailed['Kategori_Risiko'].value_counts()
print(f"\n🎯 DISTRIBUSI RISIKO:")
for risk in ['Tinggi', 'Sedang', 'Rendah']:
    if risk in risk_counts.index:
        count = risk_counts[risk]
        pct = (count / len(regional_detailed) * 100)
        icon = '🔴' if risk == 'Tinggi' else '🟡' if risk == 'Sedang' else '🟢'
        print(f"   {icon} Risiko {risk}: {count} daerah ({pct:.1f}%)")

# 3. REKOMENDASI INTERVENSI BERDASARKAN KATEGORI RISIKO
print("\n3️⃣ REKOMENDASI INTERVENSI STRATEGIS")
print("-" * 50)

print("🔴 DAERAH RISIKO TINGGI (≥30% stunting):")
high_risk_regions = regional_detailed[regional_detailed['Kategori_Risiko'] == 'Tinggi']
if len(high_risk_regions) > 0:
    print(f"   📍 Jumlah: {len(high_risk_regions)} daerah")
    print(f"   🎯 Prioritas: INTERVENSI DARURAT")
    print(f"   💡 Rekomendasi:")
    print(f"   • Program gizi darurat dan suplementasi")
    print(f"   • Screening kesehatan massal balita")
    print(f"   • Edukasi intensif ibu dan keluarga")
    print(f"   • Monitoring ketat pertumbuhan balita")
    print(f"   • Kerjasama lintas sektor (kesehatan, sosial, ekonomi)")
else:
    print(f"   ✅ Tidak ada daerah dengan risiko tinggi")

print(f"\n🟡 DAERAH RISIKO SEDANG (20-29% stunting):")
medium_risk_regions = regional_detailed[regional_detailed['Kategori_Risiko'] == 'Sedang']
if len(medium_risk_regions) > 0:
    print(f"   📍 Jumlah: {len(medium_risk_regions)} daerah")
    print(f"   🎯 Prioritas: INTERVENSI PREVENTIF")
    print(f"   💡 Rekomendasi:")
    print(f"   • Program pencegahan stunting terintegrasi")
    print(f"   • Peningkatan akses pelayanan kesehatan")
    print(f"   • Edukasi gizi dan pola asuh")
    print(f"   • Monitoring rutin pertumbuhan balita")
    print(f"   • Pemberdayaan masyarakat")
else:
    print(f"   ✅ Tidak ada daerah dengan risiko sedang")

print(f"\n🟢 DAERAH RISIKO RENDAH (<20% stunting):")
low_risk_regions = regional_detailed[regional_detailed['Kategori_Risiko'] == 'Rendah']
if len(low_risk_regions) > 0:
    print(f"   📍 Jumlah: {len(low_risk_regions)} daerah")
    print(f"   🎯 Prioritas: MAINTENANCE & BEST PRACTICE")
    print(f"   💡 Rekomendasi:")
    print(f"   • Pertahankan program yang sudah berjalan baik")
    print(f"   • Dokumentasi best practice untuk replikasi")
    print(f"   • Monitoring berkala untuk early warning")
    print(f"   • Sharing knowledge ke daerah lain")
    print(f"   • Inovasi program pencegahan")
else:
    print(f"   ⚠️ Semua daerah memerlukan intervensi")

# 4. ACTION PLAN UNTUK STAKEHOLDER
print("\n4️⃣ ACTION PLAN UNTUK STAKEHOLDER")
print("-" * 50)

print("🏛️ PEMERINTAH PROVINSI JAWA BARAT:")
print("   • Alokasi anggaran prioritas untuk daerah risiko tinggi")
print("   • Koordinasi lintas dinas untuk program terintegrasi")
print("   • Monitoring dan evaluasi berkala")
print("   • Capacity building untuk tenaga kesehatan")

print("\n🏥 DINAS KESEHATAN:")
print("   • Implementasi screening rutin menggunakan model ML")
print("   • Pelatihan tenaga kesehatan untuk early detection")
print("   • Sistem rujukan yang efektif")
print("   • Database terintegrasi untuk monitoring")

print("\n👨‍👩‍👧‍👦 MASYARAKAT DAN KELUARGA:")
print("   • Edukasi gizi dan pola asuh yang benar")
print("   • Pemantauan pertumbuhan balita secara rutin")
print("   • Partisipasi aktif dalam program posyandu")
print("   • Pelaporan dini jika ada tanda-tanda stunting")

print("\n🎓 AKADEMISI DAN PENELITI:")
print("   • Penelitian lanjutan untuk faktor risiko lokal")
print("   • Pengembangan model prediksi yang lebih akurat")
print("   • Evaluasi efektivitas intervensi")
print("   • Publikasi hasil untuk knowledge sharing")

# 5. KESIMPULAN AKHIR
print("\n" + "="*80)
print("🎯 KESIMPULAN AKHIR JABAR STUNTING ANALYZER")
print("="*80)

print(f"✅ PENCAPAIAN SISTEM:")
print(f"   • Berhasil menganalisis {len(df_clean)} data balita dari {len(regional_detailed)} daerah")
print(f"   • Model ML {best_model_name} dengan akurasi {models_results[best_model_name]['accuracy']*100:.1f}%")
print(f"   • Peta interaktif geografis dengan visualisasi risiko stunting")
print(f"   • Analisis korelasi key variables untuk validasi data")
print(f"   • Identifikasi daerah prioritas intervensi")
print(f"   • Rekomendasi strategis berbasis data")

print(f"\n🚀 DAMPAK YANG DIHARAPKAN:")
print(f"   • Penurunan prevalensi stunting di Jawa Barat")
print(f"   • Intervensi yang lebih tepat sasaran dan efisien")
print(f"   • Visualisasi interaktif untuk decision making yang lebih baik")
print(f"   • Sistem monitoring yang berbasis data dan geografis")
print(f"   • Peningkatan kualitas hidup balita Jawa Barat")

print(f"\n📊 SISTEM JABAR STUNTING ANALYZER SIAP DIGUNAKAN!")
print(f"🎯 Terima kasih telah menggunakan sistem analisis stunting Jawa Barat")
print(f"👥 Dikembangkan oleh Kelompok 9: Meiko, Syaamil, Laurensius")

print("\n" + "="*80)